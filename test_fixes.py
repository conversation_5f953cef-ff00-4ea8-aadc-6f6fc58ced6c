#!/usr/bin/env python3
"""
测试修复后的功能
"""

import sys
import os
import logging
import traceback

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from utils.logger import setup_logger, log_security_event
        from utils.exceptions import CodePrivacyError, handle_error
        from utils.config import config_manager, get_config
        from core.vscode_cleaner import VSCodeCleaner
        from core.telemetry import TelemetryManager
        from core.browser_fp import BrowserFingerprintManager
        from core.hw_identifiers import HardwareIdentifierManager
        from core.rotator import SystemParamRotator
        from core.secure_eraser import SecureEraser
        print("✓ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"✗ 模块导入失败: {str(e)}")
        traceback.print_exc()
        return False

def test_logger():
    """测试日志功能"""
    print("\n测试日志功能...")
    
    try:
        from utils.logger import setup_logger, log_security_event
        
        logger = setup_logger(debug=True)
        logger.info("测试信息日志")
        logger.warning("测试警告日志")
        logger.error("测试错误日志")
        
        log_security_event(logger, "TEST_EVENT", "测试安全事件", "INFO")
        
        print("✓ 日志功能测试成功")
        return True
    except Exception as e:
        print(f"✗ 日志功能测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_config():
    """测试配置管理"""
    print("\n测试配置管理...")
    
    try:
        from utils.config import config_manager, get_config, set_config
        
        # 测试获取配置
        log_level = get_config("general.log_level", "INFO")
        print(f"  日志级别: {log_level}")
        
        # 测试设置配置
        set_config("test.value", "test_data")
        test_value = get_config("test.value")
        
        if test_value == "test_data":
            print("✓ 配置管理测试成功")
            return True
        else:
            print("✗ 配置设置/获取不一致")
            return False
    except Exception as e:
        print(f"✗ 配置管理测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理...")
    
    try:
        from utils.exceptions import CodePrivacyError, handle_error, safe_execute
        from utils.logger import setup_logger
        
        logger = setup_logger()
        
        # 测试自定义异常
        try:
            raise CodePrivacyError("测试异常", "TEST_ERROR", "测试详细信息")
        except CodePrivacyError as e:
            success, message = handle_error(logger, "测试操作", e)
            if not success and "测试异常" in message:
                print("✓ 自定义异常处理成功")
            else:
                print("✗ 自定义异常处理失败")
                return False
        
        # 测试安全执行
        def test_func():
            return "成功结果"
        
        success, result = safe_execute(test_func, logger, "测试安全执行")
        if success and result == "成功结果":
            print("✓ 安全执行测试成功")
            return True
        else:
            print("✗ 安全执行测试失败")
            return False
            
    except Exception as e:
        print(f"✗ 错误处理测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_core_modules():
    """测试核心模块基本功能"""
    print("\n测试核心模块...")
    
    try:
        from utils.logger import setup_logger
        logger = setup_logger()
        
        # 测试VSCode清理器
        from core.vscode_cleaner import VSCodeCleaner
        vscode_cleaner = VSCodeCleaner()
        config_status = vscode_cleaner.verify_config()
        print(f"  VSCode配置状态: {config_status}")
        
        # 测试遥测管理器
        from core.telemetry import TelemetryManager
        telemetry_manager = TelemetryManager()
        machine_id = telemetry_manager.get_machine_id()
        print(f"  Machine ID: {machine_id}")
        
        # 测试硬件标识符管理器
        from core.hw_identifiers import HardwareIdentifierManager
        hw_manager = HardwareIdentifierManager()
        adapters = hw_manager.get_network_adapters()
        print(f"  网络适配器数量: {len(adapters)}")
        
        # 测试系统参数轮换器
        from core.rotator import SystemParamRotator
        rotator = SystemParamRotator()
        status = rotator.verify_rotation()
        print(f"  轮换状态: {status.split()[0] if status else '未知'}")
        
        print("✓ 核心模块基本功能测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 核心模块测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_browser_protection():
    """测试浏览器保护功能"""
    print("\n测试浏览器保护...")
    
    try:
        from core.browser_fp import BrowserFingerprintManager
        from utils.logger import setup_logger
        
        logger = setup_logger()
        browser_manager = BrowserFingerprintManager()
        
        # 测试验证保护状态（不实际修改）
        status = browser_manager.verify_protection("Chrome")
        print(f"  Chrome保护状态: {status.split()[0] if status else '未知'}")
        
        print("✓ 浏览器保护功能测试成功")
        return True
        
    except Exception as e:
        print(f"✗ 浏览器保护测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("CodePrivacy 修复验证测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_logger,
        test_config,
        test_error_handling,
        test_core_modules,
        test_browser_protection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {str(e)}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功。")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
