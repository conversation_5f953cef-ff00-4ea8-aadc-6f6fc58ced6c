@echo off
chcp 65001 >nul
echo CodePrivacy 打包脚本
echo =====================

echo.
echo 1. 检查 Python 环境...
python --version
if %errorlevel% neq 0 (
    echo ✗ Python 未安装或不在 PATH 中
    pause
    exit /b 1
)

echo.
echo 2. 检查 PyInstaller...
pyinstaller --version >nul 2>&1
if %errorlevel% neq 0 (
    echo PyInstaller 未安装，正在安装...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ✗ PyInstaller 安装失败
        pause
        exit /b 1
    )
)
echo ✓ PyInstaller 已就绪

echo.
echo 3. 清理之前的构建...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del "*.spec"
echo ✓ 清理完成

echo.
echo 4. 开始打包...
echo 这可能需要几分钟时间，请耐心等待...

pyinstaller ^
    --onefile ^
    --windowed ^
    --name CodePrivacy ^
    --add-data "core;core" ^
    --add-data "utils;utils" ^
    --hidden-import PyQt5.QtCore ^
    --hidden-import PyQt5.QtGui ^
    --hidden-import PyQt5.QtWidgets ^
    --hidden-import logging.handlers ^
    --hidden-import winreg ^
    --hidden-import psutil ^
    main.py

if %errorlevel% neq 0 (
    echo ✗ 打包失败
    pause
    exit /b 1
)

echo.
echo 5. 检查生成的文件...
if exist "dist\CodePrivacy.exe" (
    echo ✓ 可执行文件生成成功: dist\CodePrivacy.exe
    
    echo.
    echo 文件大小:
    dir "dist\CodePrivacy.exe" | findstr CodePrivacy.exe
    
    echo.
    echo 6. 创建发布包...
    if not exist "release" mkdir "release"
    copy "dist\CodePrivacy.exe" "release\"
    
    echo 创建说明文件...
    echo CodePrivacy - 开发环境隐私保护工具 > "release\README.txt"
    echo. >> "release\README.txt"
    echo 使用说明: >> "release\README.txt"
    echo 1. 双击 CodePrivacy.exe 启动程序 >> "release\README.txt"
    echo 2. 建议以管理员身份运行以获得完整功能 >> "release\README.txt"
    echo 3. 首次运行会在用户目录创建配置文件 >> "release\README.txt"
    echo. >> "release\README.txt"
    echo 注意事项: >> "release\README.txt"
    echo - 某些功能需要管理员权限 >> "release\README.txt"
    echo - 使用前建议备份重要数据 >> "release\README.txt"
    echo - 如遇问题请查看日志文件 >> "release\README.txt"
    
    echo ✓ 发布包创建完成: release\
    
    echo.
    echo 🎉 打包完成！
    echo 📁 可执行文件: dist\CodePrivacy.exe
    echo 📦 发布包: release\
    echo.
    echo 建议测试可执行文件是否正常运行。
    
) else (
    echo ✗ 未找到生成的可执行文件
    echo 请检查打包过程中的错误信息
)

echo.
pause
