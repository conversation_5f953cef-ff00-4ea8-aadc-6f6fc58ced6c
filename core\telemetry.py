import os
import json
import uuid
import platform
import logging
import subprocess

class TelemetryManager:
    """遥测管理类"""
    def __init__(self):
        self.logger = logging.getLogger("CodePrivacy")
        self.config_paths = self._get_config_paths()
    
    def _get_config_paths(self):
        """获取配置文件路径"""
        system = platform.system()
        paths = {}
        
        if system == "Windows":
            appdata = os.environ.get("APPDATA", "")
            paths["vscode"] = os.path.join(appdata, "Code", "User", "settings.json")
            paths["machine_id"] = os.path.join(appdata, "Code", "machineid")
        elif system == "Darwin":  # macOS
            home = os.path.expanduser("~")
            paths["vscode"] = os.path.join(home, "Library", "Application Support", "Code", "User", "settings.json")
            paths["machine_id"] = os.path.join(home, "Library", "Application Support", "Code", "machineid")
        else:  # Linux
            home = os.path.expanduser("~")
            paths["vscode"] = os.path.join(home, ".config", "Code", "User", "settings.json")
            paths["machine_id"] = os.path.join(home, ".config", "Code", "machineid")
        
        return paths
    
    def disable_telemetry(self):
        """禁用Telemetry"""
        try:
            vscode_settings = self.config_paths["vscode"]
            
            # 确保目录存在
            os.makedirs(os.path.dirname(vscode_settings), exist_ok=True)
            
            # 读取现有设置
            settings = {}
            if os.path.exists(vscode_settings):
                try:
                    with open(vscode_settings, 'r') as f:
                        settings = json.load(f)
                except:
                    settings = {}
            
            # 设置遥测相关选项为禁用
            settings["telemetry.enableTelemetry"] = False
            settings["telemetry.enableCrashReporter"] = False
            settings["workbench.enableExperiments"] = False
            settings["update.showReleaseNotes"] = False
            settings["workbench.settings.enableNaturalLanguageSearch"] = False
            
            # 保存更新后的设置
            with open(vscode_settings, 'w') as f:
                json.dump(settings, f, indent=4)
            
            return True, "VSCode遥测功能已禁用"
        except Exception as e:
            self.logger.error(f"禁用遥测异常: {str(e)}")
            return False, f"禁用遥测失败: {str(e)}"
    
    def get_machine_id(self):
        """获取当前Machine ID"""
        try:
            machine_id_path = self.config_paths["machine_id"]
            
            if os.path.exists(machine_id_path):
                with open(machine_id_path, 'r') as f:
                    return f.read().strip()
            
            return "未找到Machine ID"
        except Exception as e:
            self.logger.error(f"获取Machine ID异常: {str(e)}")
            return "无法读取Machine ID"
    
    def generate_new_id(self):
        """生成新的Machine ID"""
        try:
            machine_id_path = self.config_paths["machine_id"]
            
            # 生成新的UUID
            new_id = str(uuid.uuid4())
            
            # 确保目录存在
            os.makedirs(os.path.dirname(machine_id_path), exist_ok=True)
            
            # 保存新ID
            with open(machine_id_path, 'w') as f:
                f.write(new_id)
            
            return True, new_id, "已生成新的Machine ID"
        except Exception as e:
            self.logger.error(f"生成新ID异常: {str(e)}")
            return False, None, f"生成新ID失败: {str(e)}"
    
    def verify_telemetry_status(self):
        """验证遥测状态"""
        try:
            vscode_settings = self.config_paths["vscode"]
            
            if not os.path.exists(vscode_settings):
                return "VSCode设置文件不存在"
            
            try:
                with open(vscode_settings, 'r') as f:
                    settings = json.load(f)
                
                telemetry_status = []
                
                # 检查各个遥测相关设置
                telemetry_enabled = settings.get("telemetry.enableTelemetry", True)
                telemetry_status.append(f"遥测启用: {'否' if telemetry_enabled is False else '是'}")
                
                crash_reporter = settings.get("telemetry.enableCrashReporter", True)
                telemetry_status.append(f"崩溃报告: {'否' if crash_reporter is False else '是'}")
                
                experiments = settings.get("workbench.enableExperiments", True)
                telemetry_status.append(f"实验功能: {'否' if experiments is False else '是'}")
                
                # 获取Machine ID状态
                machine_id = self.get_machine_id()
                telemetry_status.append(f"Machine ID: {machine_id}")
                
                return "\n".join(telemetry_status)
            except:
                return "无法解析VSCode设置文件"
        except Exception as e:
            return f"验证遥测状态失败: {str(e)}"
