from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QComboBox, QCheckBox, QTabWidget, QTextEdit, QSpinBox,
                            QGroupBox, QFormLayout, QListWidget, QMessageBox)
from PyQt5.QtCore import pyqtSlot
import logging

class HWIdentifierTab(QWidget):
    """硬件标识符标签页"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("CodePrivacy")
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        # 创建MAC地址部分
        mac_group = QGroupBox("MAC地址修改")
        mac_layout = QVBoxLayout()
        
        # 适配器选择
        adapter_layout = QHBoxLayout()
        adapter_layout.addWidget(QLabel("选择网络适配器:"))
        self.adapter_combo = QComboBox()
        self.refresh_button = QPushButton("刷新")
        adapter_layout.addWidget(self.adapter_combo)
        adapter_layout.addWidget(self.refresh_button)
        mac_layout.addLayout(adapter_layout)
        
        # 当前MAC显示
        current_mac_layout = QHBoxLayout()
        current_mac_layout.addWidget(QLabel("当前MAC地址:"))
        self.current_mac_label = QLabel("未选择适配器")
        current_mac_layout.addWidget(self.current_mac_label)
        current_mac_layout.addStretch()
        mac_layout.addLayout(current_mac_layout)
        
        # 按钮
        mac_buttons_layout = QHBoxLayout()
        self.change_mac_button = QPushButton("更改MAC地址")
        self.verify_mac_button = QPushButton("验证MAC地址")
        mac_buttons_layout.addWidget(self.change_mac_button)
        mac_buttons_layout.addWidget(self.verify_mac_button)
        mac_layout.addLayout(mac_buttons_layout)
        
        mac_group.setLayout(mac_layout)
        
        # 创建系统UUID部分
        uuid_group = QGroupBox("系统UUID修改")
        uuid_layout = QVBoxLayout()
        
        # 当前UUID显示
        current_uuid_layout = QHBoxLayout()
        current_uuid_layout.addWidget(QLabel("当前系统UUID:"))
        self.current_uuid_label = QLabel("点击获取")
        current_uuid_layout.addWidget(self.current_uuid_label)
        current_uuid_layout.addStretch()
        uuid_layout.addLayout(current_uuid_layout)
        
        # 按钮
        uuid_buttons_layout = QHBoxLayout()
        self.get_uuid_button = QPushButton("获取UUID")
        self.change_uuid_button = QPushButton("更改UUID")
        uuid_buttons_layout.addWidget(self.get_uuid_button)
        uuid_buttons_layout.addWidget(self.change_uuid_button)
        uuid_layout.addLayout(uuid_buttons_layout)
        
        uuid_group.setLayout(uuid_layout)
        
        # 日志框
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(mac_group)
        main_layout.addWidget(uuid_group)
        main_layout.addWidget(log_group)
        
        self.setLayout(main_layout)


class VSCodeCleanerTab(QWidget):
    """VSCode清理标签页"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("CodePrivacy")
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        # 备份部分
        backup_group = QGroupBox("数据备份")
        backup_layout = QVBoxLayout()
        
        backup_desc = QLabel("在清理前，建议先备份VSCode数据")
        backup_layout.addWidget(backup_desc)
        
        self.backup_button = QPushButton("备份VSCode数据")
        backup_layout.addWidget(self.backup_button)
        
        backup_group.setLayout(backup_layout)
        
        # 清理选项部分
        clean_group = QGroupBox("清理选项")
        clean_layout = QVBoxLayout()
        
        self.clean_config_button = QPushButton("清理VSCode配置")
        self.clean_cache_button = QPushButton("清理VSCode缓存")
        self.verify_clean_button = QPushButton("验证清理状态")
        
        clean_layout.addWidget(self.clean_config_button)
        clean_layout.addWidget(self.clean_cache_button)
        clean_layout.addWidget(self.verify_clean_button)
        
        clean_group.setLayout(clean_layout)
        
        # 日志框
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(backup_group)
        main_layout.addWidget(clean_group)
        main_layout.addWidget(log_group)
        
        self.setLayout(main_layout)


class TelemetryTab(QWidget):
    """遥测管理标签页"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("CodePrivacy")
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        # 遥测控制部分
        telemetry_group = QGroupBox("遥测控制")
        telemetry_layout = QVBoxLayout()
        
        self.disable_telemetry_button = QPushButton("禁用所有遥测功能")
        self.verify_telemetry_button = QPushButton("验证遥测状态")
        
        telemetry_layout.addWidget(self.disable_telemetry_button)
        telemetry_layout.addWidget(self.verify_telemetry_button)
        
        telemetry_group.setLayout(telemetry_layout)
        
        # Machine ID部分
        machine_id_group = QGroupBox("Machine ID管理")
        machine_id_layout = QVBoxLayout()
        
        current_id_layout = QHBoxLayout()
        current_id_layout.addWidget(QLabel("当前Machine ID:"))
        self.current_id_label = QLabel("点击获取")
        current_id_layout.addWidget(self.current_id_label)
        current_id_layout.addStretch()
        machine_id_layout.addLayout(current_id_layout)
        
        self.get_id_button = QPushButton("获取Machine ID")
        self.generate_id_button = QPushButton("生成新的Machine ID")
        
        machine_id_layout.addWidget(self.get_id_button)
        machine_id_layout.addWidget(self.generate_id_button)
        
        machine_id_group.setLayout(machine_id_layout)
        
        # 日志框
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(telemetry_group)
        main_layout.addWidget(machine_id_group)
        main_layout.addWidget(log_group)
        
        self.setLayout(main_layout)


class BrowserProtectionTab(QWidget):
    """浏览器保护标签页"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("CodePrivacy")
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        # 浏览器选择
        browser_group = QGroupBox("浏览器选择")
        browser_layout = QVBoxLayout()
        
        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["Chrome", "Firefox", "Edge", "Opera"])
        browser_layout.addWidget(self.browser_combo)
        
        browser_group.setLayout(browser_layout)
        
        # 保护选项
        protection_group = QGroupBox("保护选项")
        protection_layout = QVBoxLayout()
        
        self.canvas_check = QCheckBox("Canvas指纹保护")
        self.canvas_check.setChecked(True)
        self.webgl_check = QCheckBox("WebGL指纹保护")
        self.webgl_check.setChecked(True)
        self.headers_check = QCheckBox("HTTP请求头保护")
        self.headers_check.setChecked(True)
        
        protection_layout.addWidget(self.canvas_check)
        protection_layout.addWidget(self.webgl_check)
        protection_layout.addWidget(self.headers_check)
        
        protection_group.setLayout(protection_layout)
        
        # 操作按钮
        actions_group = QGroupBox("操作")
        actions_layout = QVBoxLayout()
        
        self.apply_protection_button = QPushButton("应用保护")
        self.verify_protection_button = QPushButton("验证保护状态")
        
        actions_layout.addWidget(self.apply_protection_button)
        actions_layout.addWidget(self.verify_protection_button)
        
        actions_group.setLayout(actions_layout)
        
        # 日志框
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(browser_group)
        main_layout.addWidget(protection_group)
        main_layout.addWidget(actions_group)
        main_layout.addWidget(log_group)
        
        self.setLayout(main_layout)


class SecureEraserTab(QWidget):
    """安全擦除标签页"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("CodePrivacy")
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        # 扫描部分
        scan_group = QGroupBox("隐私数据扫描")
        scan_layout = QVBoxLayout()
        
        scan_desc = QLabel("扫描可能包含敏感信息的文件")
        scan_layout.addWidget(scan_desc)
        
        self.scan_button = QPushButton("扫描隐私数据")
        scan_layout.addWidget(self.scan_button)
        
        scan_group.setLayout(scan_layout)
        
        # 擦除部分
        erase_group = QGroupBox("安全擦除")
        erase_layout = QVBoxLayout()
        
        erase_desc = QLabel("使用多次覆写方式安全擦除数据")
        erase_layout.addWidget(erase_desc)
        
        self.erase_button = QPushButton("安全擦除数据")
        erase_layout.addWidget(self.erase_button)
        
        erase_group.setLayout(erase_layout)
        
        # 日志框
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(scan_group)
        main_layout.addWidget(erase_group)
        main_layout.addWidget(log_group)
        
        self.setLayout(main_layout)


class SystemRotatorTab(QWidget):
    """系统参数轮换标签页"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger("CodePrivacy")
        self.initUI()
    
    def initUI(self):
        """初始化UI"""
        # 轮换配置
        config_group = QGroupBox("轮换配置")
        config_layout = QFormLayout()
        
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(60, 86400)  # 60秒到24小时
        self.interval_spin.setValue(3600)  # 默认1小时
        self.interval_spin.setSuffix(" 秒")
        
        config_layout.addRow("轮换间隔:", self.interval_spin)
        
        config_group.setLayout(config_layout)
        
        # 操作按钮
        actions_group = QGroupBox("操作")
        actions_layout = QVBoxLayout()
        
        self.start_rotation_button = QPushButton("启动参数轮换")
        self.stop_rotation_button = QPushButton("停止参数轮换")
        self.verify_rotation_button = QPushButton("验证轮换状态")
        
        actions_layout.addWidget(self.start_rotation_button)
        actions_layout.addWidget(self.stop_rotation_button)
        actions_layout.addWidget(self.verify_rotation_button)
        
        actions_group.setLayout(actions_layout)
        
        # 日志框
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addWidget(config_group)
        main_layout.addWidget(actions_group)
        main_layout.addWidget(log_group)
        
        self.setLayout(main_layout)
