import os
import re
import uuid
import random
import subprocess
import platform
import logging
import time
import ctypes
from subprocess import PIPE, STDOUT

class HardwareIdentifierManager:
    """硬件标识符管理类"""
    def __init__(self):
        self.logger = logging.getLogger("CodePrivacy")
        self.is_admin = self._check_admin_privileges()

    def _check_admin_privileges(self):
        """检查管理员权限"""
        try:
            if platform.system() == "Windows":
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except Exception:
            return False
    
    def get_network_adapters(self):
        """获取网络适配器列表"""
        adapters = []
        system = platform.system()
        
        try:
            if system == "Windows":
                # Windows系统使用ipconfig
                output = subprocess.check_output("ipconfig /all", shell=True, text=True)
                # 使用正则表达式匹配适配器名称
                pattern = r"^(\w[^:]*):$"
                for line in output.split('\n'):
                    match = re.match(pattern, line.strip())
                    if match:
                        adapters.append(match.group(1))
            else:
                # Linux/macOS系统使用ifconfig
                cmd = "ifconfig" if system == "Linux" else "ifconfig -a"
                output = subprocess.check_output(cmd, shell=True, text=True)
                # 匹配适配器名称
                pattern = r"^(\w+):"
                for line in output.split('\n'):
                    match = re.match(pattern, line.strip())
                    if match:
                        adapters.append(match.group(1))
        except Exception as e:
            self.logger.error(f"获取网络适配器异常: {str(e)}")
            return []
        
        return adapters
    
    def get_current_mac(self, adapter_name):
        """获取当前MAC地址"""
        system = platform.system()
        
        try:
            if system == "Windows":
                # Windows系统使用ipconfig
                output = subprocess.check_output("ipconfig /all", shell=True, text=True)
                # 查找指定适配器的部分
                adapter_section = None
                for section in output.split('\n\n'):
                    if adapter_name in section:
                        adapter_section = section
                        break
                
                if adapter_section:
                    # 查找物理地址
                    match = re.search(r"物理地址.*?:\s*(.+)", adapter_section) or re.search(r"Physical Address.*?:\s*(.+)", adapter_section)
                    if match:
                        return match.group(1).strip()
            else:
                # Linux/macOS系统
                cmd = f"ifconfig {adapter_name}" if system == "Linux" else f"ifconfig {adapter_name}"
                output = subprocess.check_output(cmd, shell=True, text=True)
                # 查找MAC地址
                match = re.search(r"ether\s+([0-9a-f:]{17})", output) or re.search(r"HWaddr\s+([0-9a-f:]{17})", output)
                if match:
                    return match.group(1).strip()
        except Exception as e:
            self.logger.error(f"获取MAC地址异常: {str(e)}")
        
        return "未知"
    
    def change_mac_address(self, adapter_name):
        """更改MAC地址"""
        # 检查管理员权限
        if not self.is_admin:
            return False, None, "修改MAC地址需要管理员权限"

        if not adapter_name:
            return False, None, "请指定有效的网络适配器名称"

        system = platform.system()

        # 生成新的MAC地址
        new_mac = self._generate_random_mac()

        try:
            if system == "Windows":
                # Windows系统使用netsh或注册表修改
                self._windows_change_mac(adapter_name, new_mac)
            elif system == "Darwin":  # macOS
                self._macos_change_mac(adapter_name, new_mac)
            else:  # Linux
                self._linux_change_mac(adapter_name, new_mac)

            # 验证修改是否成功
            time.sleep(2)  # 等待系统应用更改
            current_mac = self.get_current_mac(adapter_name)

            # 对比(忽略格式差异，如冒号、破折号等)
            if self._normalize_mac(current_mac) == self._normalize_mac(new_mac):
                return True, new_mac, f"MAC地址已成功修改为: {new_mac}"
            else:
                return False, None, f"MAC地址修改失败，当前地址: {current_mac}"

        except PermissionError:
            return False, None, "权限不足，请以管理员身份运行程序"
        except subprocess.CalledProcessError as e:
            self.logger.error(f"命令执行失败: {str(e)}")
            return False, None, f"系统命令执行失败: {str(e)}"
        except Exception as e:
            self.logger.error(f"修改MAC地址异常: {str(e)}")
            return False, None, f"修改MAC地址时发生错误: {str(e)}"
    
    def _windows_change_mac(self, adapter_name, new_mac):
        """Windows系统修改MAC地址"""
        try:
            # 使用更安全的方法查找网络适配器
            cmd = 'wmic path win32_networkadapter get name,netconnectionid,pnpdeviceid'
            output = subprocess.check_output(cmd, shell=True, text=True, timeout=30)

            # 查找匹配的适配器
            adapter_pnp_id = None
            for line in output.split('\n'):
                if adapter_name in line:
                    # 提取PNP设备ID
                    parts = line.strip().split()
                    if len(parts) >= 3:
                        adapter_pnp_id = parts[-1]
                        break

            if not adapter_pnp_id:
                raise Exception(f"未找到适配器 {adapter_name}")

            # 禁用网络适配器
            disable_cmd = f'netsh interface set interface "{adapter_name}" admin=disable'
            subprocess.check_call(disable_cmd, shell=True, timeout=30)
            time.sleep(1)  # 等待禁用完成

            # 通过注册表修改MAC地址
            reg_query_cmd = f'reg query "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Class\\{{4D36E972-E325-11CE-BFC1-08002BE10318}}" /s /f "{adapter_pnp_id}"'
            reg_output = subprocess.check_output(reg_query_cmd, shell=True, text=True, timeout=30)

            # 查找注册表路径
            adapter_reg_path = None
            for line in reg_output.split('\n'):
                if 'HKEY_LOCAL_MACHINE' in line and '\\0' in line:
                    adapter_reg_path = line.strip()
                    break

            if not adapter_reg_path:
                raise Exception(f"未找到适配器 {adapter_name} 的注册表路径")

            # 修改MAC地址
            mac_without_colons = new_mac.replace(':', '').upper()
            set_mac_cmd = f'reg add "{adapter_reg_path}" /v NetworkAddress /t REG_SZ /d {mac_without_colons} /f'
            subprocess.check_call(set_mac_cmd, shell=True, timeout=30)

            # 启用网络适配器
            enable_cmd = f'netsh interface set interface "{adapter_name}" admin=enable'
            subprocess.check_call(enable_cmd, shell=True, timeout=30)
            time.sleep(2)  # 等待启用完成

        except subprocess.TimeoutExpired:
            raise Exception("操作超时，请检查网络适配器状态")
        except subprocess.CalledProcessError as e:
            raise Exception(f"系统命令执行失败: {e.returncode}")
    
    def _macos_change_mac(self, adapter_name, new_mac):
        """macOS系统修改MAC地址"""
        # 关闭网络适配器
        subprocess.check_call(f'sudo ifconfig {adapter_name} down', shell=True)
        
        # 修改MAC地址
        subprocess.check_call(f'sudo ifconfig {adapter_name} ether {new_mac}', shell=True)
        
        # 启用网络适配器
        subprocess.check_call(f'sudo ifconfig {adapter_name} up', shell=True)
    
    def _linux_change_mac(self, adapter_name, new_mac):
        """Linux系统修改MAC地址"""
        # 关闭网络适配器
        subprocess.check_call(f'sudo ifconfig {adapter_name} down', shell=True)
        
        # 修改MAC地址
        subprocess.check_call(f'sudo ifconfig {adapter_name} hw ether {new_mac}', shell=True)
        
        # 启用网络适配器
        subprocess.check_call(f'sudo ifconfig {adapter_name} up', shell=True)
    
    def _generate_random_mac(self):
        """生成随机MAC地址"""
        # 生成随机MAC地址
        mac = [random.randint(0, 255) for _ in range(6)]
        # 确保第一个字节的第二位为0(表示本地地址)
        mac[0] = (mac[0] & 0xfc) | 0x02
        
        # 格式化为xx:xx:xx:xx:xx:xx
        return ':'.join(f'{x:02x}' for x in mac)
    
    def _normalize_mac(self, mac):
        """标准化MAC地址格式"""
        if not mac:
            return ""
        # 只保留十六进制字符
        return ''.join(c for c in mac.lower() if c in '0123456789abcdef')
    
    def get_current_uuid(self):
        """获取当前系统UUID"""
        system = platform.system()
        
        try:
            if system == "Windows":
                # Windows系统使用wmic
                output = subprocess.check_output("wmic csproduct get uuid", shell=True, text=True)
                # 解析输出
                for line in output.split('\n'):
                    if "UUID" not in line and line.strip():
                        return line.strip()
            elif system == "Darwin":  # macOS
                # macOS使用ioreg
                output = subprocess.check_output("ioreg -rd1 -c IOPlatformExpertDevice | grep -i 'UUID'", shell=True, text=True)
                match = re.search(r'"UUID"\s*=\s*"(.+?)"', output)
                if match:
                    return match.group(1)
            else:  # Linux
                # Linux使用dmidecode
                output = subprocess.check_output("sudo dmidecode -s system-uuid", shell=True, text=True)
                return output.strip()
        except Exception as e:
            self.logger.error(f"获取系统UUID异常: {str(e)}")
        
        return "未知"
    
    def change_system_uuid(self):
        """更改系统UUID"""
        try:
            # 生成新UUID
            new_uuid = str(uuid.uuid4())
            system = platform.system()
            
            if system == "Windows":
                self._windows_change_uuid(new_uuid)
            elif system == "Darwin":  # macOS
                self._macos_change_uuid(new_uuid)
            else:  # Linux
                self._linux_change_uuid(new_uuid)
            
            # 验证修改是否成功
            time.sleep(1)  # 等待系统应用更改
            current_uuid = self.get_current_uuid()
            
            if current_uuid.lower() == new_uuid.lower():
                return True, new_uuid, f"系统UUID已成功修改为: {new_uuid}"
            else:
                # 某些系统可能需要重启才能生效
                return True, new_uuid, f"系统UUID已设置为: {new_uuid}，可能需要重启系统才能生效"
        
        except Exception as e:
            self.logger.error(f"修改系统UUID异常: {str(e)}")
            return False, None, f"修改系统UUID时发生错误: {str(e)}"
    
    def _windows_change_uuid(self, new_uuid):
        """Windows系统修改UUID"""
        # 需要修改注册表
        reg_path = "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography"
        subprocess.check_call(f'reg add "{reg_path}" /v MachineGuid /t REG_SZ /d {new_uuid} /f', shell=True)
        
        # 还需要修改SMBIOS系统信息
        # 注意: 这通常需要重启才能生效，且可能需要特殊权限
        try:
            reg_path = "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\SystemInformation"
            subprocess.check_call(f'reg add "{reg_path}" /v ComputerHardwareId /t REG_SZ /d {{{new_uuid}}} /f', shell=True)
        except:
            # 如果上述修改失败，尝试使用替代方法
            self.logger.warning("无法修改SMBIOS UUID，尝试替代方法")
    
    def _macos_change_uuid(self, new_uuid):
        """macOS系统修改UUID"""
        # macOS中通常不允许直接修改硬件UUID，但可以修改某些软件UUID
        # 修改IOPlatformUUID需要特殊权限，且可能会影响系统稳定性
        # 这里提供一个相对安全的替代方法
        uuid_plist = os.path.expanduser("~/Library/Preferences/com.apple.SystemProfiler.plist")
        subprocess.check_call(f'defaults write {uuid_plist} "CPU Names" -dict-add "UUID" "{new_uuid}"', shell=True)
    
    def _linux_change_uuid(self, new_uuid):
        """Linux系统修改UUID"""
        # Linux系统修改UUID通常需要修改/etc/machine-id文件
        # 注意：这可能需要root权限
        with open("/etc/machine-id", "w") as f:
            f.write(new_uuid.replace("-", ""))
        
        # 如果存在，也修改dbus machine-id
        dbus_machine_id = "/var/lib/dbus/machine-id"
        if os.path.exists(dbus_machine_id):
            try:
                with open(dbus_machine_id, "w") as f:
                    f.write(new_uuid.replace("-", ""))
            except:
                self.logger.warning("无法修改DBUS machine-id，可能需要root权限")
