from PyQt5.QtWidgets import QMainWindow, QTabWidget, QAction, QMenu, QMessageBox
from PyQt5.QtCore import Qt
import logging

from components.tab_widgets import (HWIdentifierTab, VSCodeCleanerTab, TelemetryTab, 
                                  BrowserProtectionTab, SecureEraserTab, SystemRotatorTab)
from components.progress_dialog import ProgressDialog
from components.worker import (HWIdentifierWorker, VSCodeWorker, TelemetryWorker,
                             BrowserWorker, SecureEraserWorker, RotatorWorker)

class MainWindow(QMainWindow):
    """主窗口类"""
    def __init__(self, hw_manager, vscode_cleaner, telemetry_manager, 
                browser_manager, secure_eraser, system_rotator):
        super().__init__()
        
        self.logger = logging.getLogger("CodePrivacy")
        
        # 保存管理类实例
        self.hw_manager = hw_manager
        self.vscode_cleaner = vscode_cleaner
        self.telemetry_manager = telemetry_manager
        self.browser_manager = browser_manager
        self.secure_eraser = secure_eraser
        self.system_rotator = system_rotator
        
        # 当前活动的工作线程
        self.current_worker = None
        
        # 初始化UI
        self.initUI()
        
        # 连接信号槽
        self.connectSignalsSlots()
    
    def initUI(self):
        """初始化UI"""
        self.setWindowTitle("CodePrivacy - 开发者隐私保护工具")
        self.setMinimumSize(800, 600)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 添加各个功能标签页
        self.hw_tab = HWIdentifierTab()
        self.vscode_tab = VSCodeCleanerTab()
        self.telemetry_tab = TelemetryTab()
        self.browser_tab = BrowserProtectionTab()
        self.eraser_tab = SecureEraserTab()
        self.rotator_tab = SystemRotatorTab()
        
        self.tab_widget.addTab(self.hw_tab, "硬件标识符")
        self.tab_widget.addTab(self.vscode_tab, "VSCode清理")
        self.tab_widget.addTab(self.telemetry_tab, "遥测管理")
        self.tab_widget.addTab(self.browser_tab, "浏览器保护")
        self.tab_widget.addTab(self.eraser_tab, "安全擦除")
        self.tab_widget.addTab(self.rotator_tab, "系统轮换")
        
        self.setCentralWidget(self.tab_widget)
        
        # 创建菜单栏
        self.createMenus()
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def createMenus(self):
        """创建菜单"""
        # 文件菜单
        file_menu = self.menuBar().addMenu("文件")
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 帮助菜单
        help_menu = self.menuBar().addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.showAboutDialog)
        help_menu.addAction(about_action)
    
    def connectSignalsSlots(self):
        """连接信号和槽"""
        # 硬件标识符标签页
        self.hw_tab.refresh_button.clicked.connect(self.refreshAdapters)
        self.hw_tab.adapter_combo.currentIndexChanged.connect(self.updateCurrentMAC)
        self.hw_tab.change_mac_button.clicked.connect(self.changeMAC)
        self.hw_tab.verify_mac_button.clicked.connect(self.verifyMAC)
        self.hw_tab.get_uuid_button.clicked.connect(self.getUUID)
        self.hw_tab.change_uuid_button.clicked.connect(self.changeUUID)
        
        # VSCode清理标签页
        self.vscode_tab.backup_button.clicked.connect(self.backupVSCodeData)
        self.vscode_tab.clean_config_button.clicked.connect(self.cleanVSCodeConfig)
        self.vscode_tab.clean_cache_button.clicked.connect(self.cleanVSCodeCache)
        self.vscode_tab.verify_clean_button.clicked.connect(self.verifyVSCodeClean)
        
        # 遥测管理标签页
        self.telemetry_tab.disable_telemetry_button.clicked.connect(self.disableTelemetry)
        self.telemetry_tab.verify_telemetry_button.clicked.connect(self.verifyTelemetry)
        self.telemetry_tab.get_id_button.clicked.connect(self.getMachineID)
        self.telemetry_tab.generate_id_button.clicked.connect(self.generateNewID)
        
        # 浏览器保护标签页
        self.browser_tab.apply_protection_button.clicked.connect(self.applyBrowserProtection)
        self.browser_tab.verify_protection_button.clicked.connect(self.verifyBrowserProtection)
        
        # 安全擦除标签页
        self.eraser_tab.scan_button.clicked.connect(self.scanResidualData)
        self.eraser_tab.erase_button.clicked.connect(self.eraseData)
        
        # 系统参数轮换标签页
        self.rotator_tab.start_rotation_button.clicked.connect(self.startRotation)
        self.rotator_tab.stop_rotation_button.clicked.connect(self.stopRotation)
        self.rotator_tab.verify_rotation_button.clicked.connect(self.verifyRotation)
    
    def showAboutDialog(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于CodePrivacy", 
                         "CodePrivacy - 开发者隐私保护工具\n\n"
                         "版本: 1.0.0\n\n"
                         "保护您的开发环境隐私安全")
    
    # 以下是各个功能的实现...
    # 硬件标识符相关方法
    def refreshAdapters(self):
        """刷新网络适配器列表"""
        try:
            adapters = self.hw_manager.get_network_adapters()
            
            self.hw_tab.adapter_combo.clear()
            self.hw_tab.adapter_combo.addItems(adapters)
            
            self.hw_tab.log_text.append("已刷新网络适配器列表")
        except Exception as e:
            self.hw_tab.log_text.append(f"刷新适配器列表失败: {str(e)}")
    
    def updateCurrentMAC(self):
        """更新当前MAC地址显示"""
        adapter = self.hw_tab.adapter_combo.currentText()
        if not adapter:
            return
        
        try:
            mac = self.hw_manager.get_current_mac(adapter)
            self.hw_tab.current_mac_label.setText(mac)
            self.hw_tab.log_text.append(f"当前适配器 {adapter} 的MAC地址: {mac}")
        except Exception as e:
            self.hw_tab.log_text.append(f"获取MAC地址失败: {str(e)}")
    
    def changeMAC(self):
        """更改MAC地址"""
        adapter = self.hw_tab.adapter_combo.currentText()
        if not adapter:
            QMessageBox.warning(self, "警告", "请先选择网络适配器")
            return
        
        # 创建并配置工作线程
        self.current_worker = HWIdentifierWorker(self.hw_manager, "change_mac", adapter)
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.hw_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onHWOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "更改MAC地址")
        progress_dialog.set_status(f"正在更改适配器 {adapter} 的MAC地址...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
        
# components/main_window.py 继续上一部分

    def verifyMAC(self):
        """验证MAC地址"""
        adapter = self.hw_tab.adapter_combo.currentText()
        if not adapter:
            QMessageBox.warning(self, "警告", "请先选择网络适配器")
            return
        
        try:
            mac = self.hw_manager.get_current_mac(adapter)
            self.hw_tab.log_text.append(f"验证MAC地址: {adapter} 的当前MAC为 {mac}")
        except Exception as e:
            self.hw_tab.log_text.append(f"验证MAC地址失败: {str(e)}")
    
    def getUUID(self):
        """获取系统UUID"""
        try:
            uuid = self.hw_manager.get_system_uuid()
            self.hw_tab.current_uuid_label.setText(uuid)
            self.hw_tab.log_text.append(f"当前系统UUID: {uuid}")
        except Exception as e:
            self.hw_tab.log_text.append(f"获取UUID失败: {str(e)}")
    
    def changeUUID(self):
        """更改系统UUID"""
        # 创建并配置工作线程
        self.current_worker = HWIdentifierWorker(self.hw_manager, "change_uuid")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.hw_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onHWOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "更改系统UUID")
        progress_dialog.set_status("正在更改系统UUID...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def onHWOperationFinished(self, success, message):
        """硬件操作完成处理"""
        if success:
            self.hw_tab.log_text.append(f"操作成功: {message}")
            
            # 刷新显示
            if self.current_worker.action == "change_mac":
                self.updateCurrentMAC()
            elif self.current_worker.action == "change_uuid":
                self.getUUID()
        else:
            self.hw_tab.log_text.append(f"操作失败: {message}")
        
        self.current_worker = None
    
    # VSCode清理相关方法
    def backupVSCodeData(self):
        """备份VSCode数据"""
        # 创建并配置工作线程
        self.current_worker = VSCodeWorker(self.vscode_cleaner, "backup")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.vscode_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onVSCodeOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "备份VSCode数据")
        progress_dialog.set_status("正在备份VSCode数据...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def cleanVSCodeConfig(self):
        """清理VSCode配置"""
        reply = QMessageBox.question(self, "确认操作", 
                                   "清理VSCode配置可能会导致您的设置丢失。是否继续?",
                                   QMessageBox.Yes | QMessageBox.No, 
                                   QMessageBox.No)
        
        if reply == QMessageBox.No:
            return
        
        # 创建并配置工作线程
        self.current_worker = VSCodeWorker(self.vscode_cleaner, "clean_config")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.vscode_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onVSCodeOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "清理VSCode配置")
        progress_dialog.set_status("正在清理VSCode配置...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def cleanVSCodeCache(self):
        """清理VSCode缓存"""
        # 创建并配置工作线程
        self.current_worker = VSCodeWorker(self.vscode_cleaner, "clean_cache")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.vscode_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onVSCodeOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "清理VSCode缓存")
        progress_dialog.set_status("正在清理VSCode缓存...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def verifyVSCodeClean(self):
        """验证VSCode清理状态"""
        try:
            config_status = self.vscode_cleaner.verify_config()
            cache_status = self.vscode_cleaner.verify_cache()
            
            self.vscode_tab.log_text.append("VSCode配置状态:")
            self.vscode_tab.log_text.append(config_status)
            self.vscode_tab.log_text.append("\nVSCode缓存状态:")
            self.vscode_tab.log_text.append(cache_status)
        except Exception as e:
            self.vscode_tab.log_text.append(f"验证清理状态失败: {str(e)}")
    
    def onVSCodeOperationFinished(self, success, message):
        """VSCode操作完成处理"""
        if success:
            self.vscode_tab.log_text.append(f"操作成功: {message}")
        else:
            self.vscode_tab.log_text.append(f"操作失败: {message}")
        
        self.current_worker = None
    
    # 遥测管理相关方法
    def disableTelemetry(self):
        """禁用遥测"""
        # 创建并配置工作线程
        self.current_worker = TelemetryWorker(self.telemetry_manager, "disable")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.telemetry_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onTelemetryOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "禁用遥测")
        progress_dialog.set_status("正在禁用遥测功能...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def verifyTelemetry(self):
        """验证遥测状态"""
        try:
            status = self.telemetry_manager.verify_telemetry_status()
            self.telemetry_tab.log_text.append("遥测状态:")
            self.telemetry_tab.log_text.append(status)
        except Exception as e:
            self.telemetry_tab.log_text.append(f"验证遥测状态失败: {str(e)}")
    
    def getMachineID(self):
        """获取Machine ID"""
        try:
            machine_id = self.telemetry_manager.get_machine_id()
            self.telemetry_tab.current_id_label.setText(machine_id)
            self.telemetry_tab.log_text.append(f"当前Machine ID: {machine_id}")
        except Exception as e:
            self.telemetry_tab.log_text.append(f"获取Machine ID失败: {str(e)}")
    
    def generateNewID(self):
        """生成新的Machine ID"""
        # 创建并配置工作线程
        self.current_worker = TelemetryWorker(self.telemetry_manager, "new_id")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.telemetry_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onTelemetryOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "生成新ID")
        progress_dialog.set_status("正在生成新的Machine ID...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def onTelemetryOperationFinished(self, success, message):
        """遥测操作完成处理"""
        if success:
            self.telemetry_tab.log_text.append(f"操作成功: {message}")
            
            # 刷新显示
            if self.current_worker.action == "new_id":
                self.getMachineID()
        else:
            self.telemetry_tab.log_text.append(f"操作失败: {message}")
        
        self.current_worker = None
    
    # 浏览器保护相关方法
    def applyBrowserProtection(self):
        """应用浏览器保护"""
        browser = self.browser_tab.browser_combo.currentText()
        
        # 获取保护选项
        options = {
            "canvas": self.browser_tab.canvas_check.isChecked(),
            "webgl": self.browser_tab.webgl_check.isChecked(),
            "headers": self.browser_tab.headers_check.isChecked()
        }
        
        # 创建并配置工作线程
        self.current_worker = BrowserWorker(self.browser_manager, "protect", browser, options)
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.browser_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onBrowserOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "应用浏览器保护")
        progress_dialog.set_status(f"正在为{browser}应用保护...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def verifyBrowserProtection(self):
        """验证浏览器保护状态"""
        browser = self.browser_tab.browser_combo.currentText()
        
        try:
            status = self.browser_manager.verify_protection(browser)
            self.browser_tab.log_text.append(f"{browser}保护状态:")
            self.browser_tab.log_text.append(status)
        except Exception as e:
            self.browser_tab.log_text.append(f"验证保护状态失败: {str(e)}")
    
    def onBrowserOperationFinished(self, success, message):
        """浏览器操作完成处理"""
        if success:
            self.browser_tab.log_text.append(f"操作成功: {message}")
        else:
            self.browser_tab.log_text.append(f"操作失败: {message}")
        
        self.current_worker = None
    
    # 安全擦除相关方法
    def scanResidualData(self):
        """扫描残留数据"""
        # 创建并配置工作线程
        self.current_worker = SecureEraserWorker(self.secure_eraser, "scan")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.eraser_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onEraserOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "扫描残留数据")
        progress_dialog.set_status("正在扫描可能包含隐私信息的文件...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def eraseData(self):
        """擦除数据"""
        reply = QMessageBox.question(self, "确认操作", 
                                   "安全擦除将永久删除扫描到的文件，无法恢复。是否继续?",
                                   QMessageBox.Yes | QMessageBox.No, 
                                   QMessageBox.No)
        
        if reply == QMessageBox.No:
            return
        
        # 创建并配置工作线程
        self.current_worker = SecureEraserWorker(self.secure_eraser, "erase")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.eraser_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onEraserOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "安全擦除数据")
        progress_dialog.set_status("正在安全擦除文件...")
        
        # 连接对话框和工作线程
        self.current_worker.progressChanged.connect(progress_dialog.set_progress)
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def onEraserOperationFinished(self, success, message):
        """擦除操作完成处理"""
        if success:
            self.eraser_tab.log_text.append(f"操作成功: {message}")
        else:
            self.eraser_tab.log_text.append(f"操作失败: {message}")
        
        self.current_worker = None
    
    # 系统参数轮换相关方法
    def startRotation(self):
        """启动参数轮换"""
        interval = self.rotator_tab.interval_spin.value()
        
        # 创建并配置工作线程
        self.current_worker = RotatorWorker(self.system_rotator, "start", interval)
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.rotator_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onRotatorOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "启动参数轮换")
        progress_dialog.set_status("正在启动系统参数轮换...")
        
        # 连接对话框和工作线程
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def stopRotation(self):
        """停止参数轮换"""
        # 创建并配置工作线程
        self.current_worker = RotatorWorker(self.system_rotator, "stop")
        
        # 连接信号
        self.current_worker.statusChanged.connect(lambda msg: self.rotator_tab.log_text.append(msg))
        self.current_worker.finished.connect(self.onRotatorOperationFinished)
        
        # 创建进度对话框
        progress_dialog = ProgressDialog(self, "停止参数轮换")
        progress_dialog.set_status("正在停止系统参数轮换...")
        
        # 连接对话框和工作线程
        self.current_worker.statusChanged.connect(progress_dialog.set_status)
        
        # 启动工作线程和显示对话框
        self.current_worker.start()
        progress_dialog.start()
    
    def verifyRotation(self):
        """验证轮换状态"""
        try:
            status = self.system_rotator.verify_rotation()
            self.rotator_tab.log_text.append("参数轮换状态:")
            self.rotator_tab.log_text.append(status)
        except Exception as e:
            self.rotator_tab.log_text.append(f"验证轮换状态失败: {str(e)}")
    
    def onRotatorOperationFinished(self, success, message):
        """轮换操作完成处理"""
        if success:
            self.rotator_tab.log_text.append(f"操作成功: {message}")
        else:
            self.rotator_tab.log_text.append(f"操作失败: {message}")
        
        self.current_worker = None
    
    def closeEvent(self, event):
        """关闭窗口事件处理"""
        # 如果有正在运行的工作线程，提示用户
        if self.current_worker and self.current_worker.isRunning():
            reply = QMessageBox.question(self, "确认退出", 
                                       "有任务正在运行，确定要退出吗?",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                # 停止当前工作
                if self.current_worker:
                    self.current_worker.cancel()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
