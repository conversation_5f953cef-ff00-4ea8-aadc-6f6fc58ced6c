import os
import logging
import platform
from datetime import datetime

def setup_logger():
    """设置日志记录器"""
    logger = logging.getLogger("CodePrivacy")
    logger.setLevel(logging.INFO)
    
    # 确保日志目录存在
    log_dir = get_log_dir()
    os.makedirs(log_dir, exist_ok=True)
    
    # 文件处理器
    log_file = os.path.join(log_dir, f"codeprivacy_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.addHandler(console_handler)
    
    return logger

def get_log_dir():
    """获取日志目录"""
    system = platform.system()
    if system == "Windows":
        base_dir = os.path.join(os.environ["APPDATA"], "CodePrivacy", "logs")
    else:
        base_dir = os.path.expanduser(os.path.join("~", "CodePrivacy", "logs"))
    return base_dir

def log_operation(logger, operation, success, message, details=None):
    """记录操作日志"""
    log_entry = {
        "operation": operation,
        "success": success,
        "message": message,
        "timestamp": datetime.now().isoformat(),
        "details": details
    }
    
    if success:
        logger.info(f"{operation}: {message}")
    else:
        logger.error(f"{operation} 失败: {message}")
    
    return log_entry
