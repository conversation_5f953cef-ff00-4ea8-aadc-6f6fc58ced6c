import os
import logging
import platform
import sys
import traceback
from datetime import datetime
from logging.handlers import RotatingFileHandler

def setup_logger(debug=False):
    """设置日志记录器"""
    logger = logging.getLogger("CodePrivacy")

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 设置日志级别
    logger.setLevel(logging.DEBUG if debug else logging.INFO)

    # 确保日志目录存在
    log_dir = get_log_dir()
    os.makedirs(log_dir, exist_ok=True)

    # 文件处理器 - 使用轮转日志
    log_file = os.path.join(log_dir, "codeprivacy.log")
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG if debug else logging.INFO)

    # 详细的文件日志格式
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 简洁的控制台日志格式
    console_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # 添加未捕获异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        logger.critical("未捕获的异常", exc_info=(exc_type, exc_value, exc_traceback))

    sys.excepthook = handle_exception

    return logger

def get_log_dir():
    """获取日志目录"""
    try:
        # 尝试使用配置管理器
        from utils.config import get_log_dir as config_get_log_dir
        return config_get_log_dir()
    except ImportError:
        # 回退到默认实现
        system = platform.system()
        if system == "Windows":
            base_dir = os.path.join(os.environ.get("APPDATA", ""), "CodePrivacy", "logs")
        else:
            base_dir = os.path.expanduser(os.path.join("~", "CodePrivacy", "logs"))
        os.makedirs(base_dir, exist_ok=True)
        return base_dir

def log_operation(logger, operation, success, message, details=None, exc_info=None):
    """记录操作日志"""
    log_entry = {
        "operation": operation,
        "success": success,
        "message": message,
        "timestamp": datetime.now().isoformat(),
        "details": details
    }

    if success:
        logger.info(f"{operation}: {message}")
        if details:
            logger.debug(f"{operation} 详细信息: {details}")
    else:
        logger.error(f"{operation} 失败: {message}", exc_info=exc_info)
        if details:
            logger.debug(f"{operation} 失败详细信息: {details}")

    return log_entry

def log_security_event(logger, event_type, description, severity="INFO"):
    """记录安全相关事件"""
    security_msg = f"[SECURITY-{severity}] {event_type}: {description}"

    if severity == "CRITICAL":
        logger.critical(security_msg)
    elif severity == "ERROR":
        logger.error(security_msg)
    elif severity == "WARNING":
        logger.warning(security_msg)
    else:
        logger.info(security_msg)

def get_system_info():
    """获取系统信息用于日志"""
    return {
        "platform": platform.platform(),
        "python_version": platform.python_version(),
        "architecture": platform.architecture()[0]
    }
