@echo off
echo 测试 CodePrivacy 可执行文件
echo ================================

if exist "dist\CodePrivacy.exe" (
    echo ✓ 找到可执行文件: dist\CodePrivacy.exe
    
    echo.
    echo 文件信息:
    dir "dist\CodePrivacy.exe"
    
    echo.
    echo 正在启动程序...
    echo 注意: 程序将在后台运行，请检查是否有GUI窗口出现
    echo.
    
    start "" "dist\CodePrivacy.exe"
    
    echo 程序已启动。如果没有看到窗口，可能需要：
    echo 1. 以管理员身份运行
    echo 2. 检查防病毒软件是否阻止了程序
    echo 3. 查看任务管理器中是否有 CodePrivacy.exe 进程
    
) else (
    echo ✗ 未找到可执行文件
    echo 请先运行打包命令生成可执行文件
)

echo.
pause
