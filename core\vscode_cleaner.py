import os
import shutil
import platform
import logging
from datetime import datetime

class VSCodeCleaner:
    """VSCode清理类"""
    def __init__(self):
        self.logger = logging.getLogger("CodePrivacy")
        self.vscode_paths = self._get_vscode_paths()
        self.backup_dir = self._get_backup_dir()
    
    def _get_vscode_paths(self):
        """获取VSCode配置和缓存路径"""
        system = platform.system()
        paths = {}
        
        if system == "Windows":
            appdata = os.environ.get("APPDATA", "")
            paths["config"] = os.path.join(appdata, "Code", "User")
            paths["cache"] = os.path.join(appdata, "Code", "Cache")
        elif system == "Darwin":  # macOS
            home = os.path.expanduser("~")
            paths["config"] = os.path.join(home, "Library", "Application Support", "Code", "User")
            paths["cache"] = os.path.join(home, "Library", "Application Support", "Code", "Cache")
        else:  # Linux
            home = os.path.expanduser("~")
            paths["config"] = os.path.join(home, ".config", "Code", "User")
            paths["cache"] = os.path.join(home, ".config", "Code", "Cache")
        
        return paths
    
    def _get_backup_dir(self):
        """获取备份目录"""
        system = platform.system()
        if system == "Windows":
            base_dir = os.path.join(os.environ.get("APPDATA", ""), "CodePrivacy", "backups")
        else:
            base_dir = os.path.expanduser(os.path.join("~", "CodePrivacy", "backups"))
        
        return base_dir
    
    def backup_data(self):
        """备份VSCode数据"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(self.backup_dir, f"vscode_backup_{timestamp}")
            
            os.makedirs(backup_path, exist_ok=True)
            
            # 备份配置
            if os.path.exists(self.vscode_paths["config"]):
                config_backup = os.path.join(backup_path, "config")
                shutil.copytree(self.vscode_paths["config"], config_backup)
            
            # 备份缓存
            if os.path.exists(self.vscode_paths["cache"]):
                cache_backup = os.path.join(backup_path, "cache")
                shutil.copytree(self.vscode_paths["cache"], cache_backup)
            
            return True, f"VSCode数据已备份到 {backup_path}"
        except Exception as e:
            self.logger.error(f"备份VSCode数据异常: {str(e)}")
            return False, f"备份失败: {str(e)}"
    
    def clean_config(self):
        """清理VSCode配置"""
        try:
            config_path = self.vscode_paths["config"]
            
            # 清理特定配置文件
            sensitive_files = [
                "settings.json",
                "keybindings.json",
                "globalStorage",
                "workspaceStorage"
            ]
            
            for file in sensitive_files:
                file_path = os.path.join(config_path, file)
                if os.path.exists(file_path):
                    if os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        os.makedirs(file_path)
                    else:
                        # 对于JSON文件，创建干净的版本
                        with open(file_path, 'w') as f:
                            f.write('{}')
            
            return True, "VSCode配置已清理"
        except Exception as e:
            self.logger.error(f"清理VSCode配置异常: {str(e)}")
            return False, f"清理配置失败: {str(e)}"
    
    def clean_cache(self):
        """清理VSCode缓存"""
        try:
            cache_path = self.vscode_paths["cache"]
            
            if os.path.exists(cache_path):
                # 删除缓存目录内容
                for item in os.listdir(cache_path):
                    item_path = os.path.join(cache_path, item)
                    try:
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                        else:
                            os.remove(item_path)
                    except:
                        pass
            
            # 清理存储历史记录的文件
            history_file = os.path.join(self.vscode_paths["config"], "storage.json")
            if os.path.exists(history_file):
                with open(history_file, 'w') as f:
                    f.write('{}')
            
            return True, "VSCode缓存已清理"
        except Exception as e:
            self.logger.error(f"清理VSCode缓存异常: {str(e)}")
            return False, f"清理缓存失败: {str(e)}"
    
    def verify_config(self):
        """验证配置清理状态"""
        try:
            config_path = self.vscode_paths["config"]
            sensitive_files = ["settings.json", "keybindings.json"]
            results = []
            
            for file in sensitive_files:
                file_path = os.path.join(config_path, file)
                if os.path.exists(file_path):
                    with open(file_path, 'r') as f:
                        content = f.read().strip()
                        if content == '{}':
                            results.append(f"{file}: 已清理")
                        else:
                            results.append(f"{file}: 包含配置")
                else:
                    results.append(f"{file}: 不存在")
            
            # 检查工作区存储
            workspace_storage = os.path.join(config_path, "workspaceStorage")
            if os.path.exists(workspace_storage) and len(os.listdir(workspace_storage)) == 0:
                results.append("工作区存储: 已清理")
            else:
                results.append("工作区存储: 包含数据")
            
            return "\n".join(results)
        except Exception as e:
            return f"验证配置失败: {str(e)}"
    
    def verify_cache(self):
        """验证缓存清理状态"""
        try:
            cache_path = self.vscode_paths["cache"]
            
            if not os.path.exists(cache_path) or len(os.listdir(cache_path)) == 0:
                return "缓存目录: 已清理"
            else:
                return f"缓存目录: 包含 {len(os.listdir(cache_path))} 个项目"
        except Exception as e:
            return f"验证缓存失败: {str(e)}"
