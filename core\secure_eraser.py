import os
import random
import platform
import logging
import shutil
import glob

class SecureEraser:
    """安全擦除类"""
    def __init__(self):
        self.logger = logging.getLogger("CodePrivacy")
        self.vscode_paths = self._get_vscode_paths()
        self.residual_files = []
    
    def _get_vscode_paths(self):
        """获取VSCode相关路径"""
        system = platform.system()
        paths = {}
        
        if system == "Windows":
            appdata = os.environ.get("APPDATA", "")
            localappdata = os.environ.get("LOCALAPPDATA", "")
            temp = os.environ.get("TEMP", "")
            
            paths["user_data"] = os.path.join(appdata, "Code")
            paths["logs"] = os.path.join(appdata, "Code", "logs")
            paths["crash_dumps"] = os.path.join(appdata, "Code", "Crashpad")
            paths["temp"] = temp
        
        elif system == "Darwin":  # macOS
            home = os.path.expanduser("~")
            
            paths["user_data"] = os.path.join(home, "Library", "Application Support", "Code")
            paths["logs"] = os.path.join(home, "Library", "Logs", "Code")
            paths["crash_dumps"] = os.path.join(home, "Library", "Application Support", "Code", "Crashpad")
            paths["temp"] = "/tmp"
        
        else:  # Linux
            home = os.path.expanduser("~")
            
            paths["user_data"] = os.path.join(home, ".config", "Code")
            paths["logs"] = os.path.join(home, ".config", "Code", "logs")
            paths["crash_dumps"] = os.path.join(home, ".config", "Code", "Crashpad")
            paths["temp"] = "/tmp"
        
        return paths
    
    def scan_residual_data(self):
        """扫描残留数据"""
        try:
            self.residual_files = []
            found_files = 0
            
            # 扫描日志文件
            if os.path.exists(self.vscode_paths["logs"]):
                log_files = glob.glob(os.path.join(self.vscode_paths["logs"], "**"), recursive=True)
                self.residual_files.extend(log_files)
                found_files += len(log_files)
            
            # 扫描崩溃转储
            if os.path.exists(self.vscode_paths["crash_dumps"]):
                dump_files = glob.glob(os.path.join(self.vscode_paths["crash_dumps"], "**"), recursive=True)
                self.residual_files.extend(dump_files)
                found_files += len(dump_files)
            
            # 扫描临时文件
            vscode_temp_pattern = os.path.join(self.vscode_paths["temp"], "vscode-*")
            temp_files = glob.glob(vscode_temp_pattern)
            self.residual_files.extend(temp_files)
            found_files += len(temp_files)
            
            # 扫描遥测数据
            telemetry_path = os.path.join(self.vscode_paths["user_data"], "machineid")
            if os.path.exists(telemetry_path):
                self.residual_files.append(telemetry_path)
                found_files += 1
            
            return True, f"找到 {found_files} 个可能包含隐私信息的文件"
        
        except Exception as e:
            self.logger.error(f"扫描残留数据异常: {str(e)}")
            return False, f"扫描残留数据失败: {str(e)}"
    
    def erase_data(self):
        """安全擦除数据"""
        try:
            if not self.residual_files:
                # 如果没有扫描，先进行扫描
                self.scan_residual_data()
            
            erased_count = 0
            
            for file_path in self.residual_files:
                if os.path.isfile(file_path):
                    # 对文件进行安全擦除
                    self._secure_erase_file(file_path)
                    erased_count += 1
                elif os.path.isdir(file_path):
                    # 对目录进行安全擦除
                    for root, _, files in os.walk(file_path):
                        for file in files:
                            full_path = os.path.join(root, file)
                            self._secure_erase_file(full_path)
                            erased_count += 1
            
            return True, f"已安全擦除 {erased_count} 个文件"
        
        except Exception as e:
            self.logger.error(f"擦除数据异常: {str(e)}")
            return False, f"擦除数据失败: {str(e)}"
    
    def _secure_erase_file(self, file_path):
        """安全擦除单个文件"""
        try:
            # 检查文件是否存在和可访问
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                return True  # 文件不存在视为已擦除

            if not os.path.isfile(file_path):
                self.logger.warning(f"路径不是文件: {file_path}")
                return False

            # 检查文件权限
            if not os.access(file_path, os.W_OK):
                self.logger.error(f"无权限写入文件: {file_path}")
                return False

            # 获取文件大小
            file_size = os.path.getsize(file_path)

            # 对于大文件，分块处理以避免内存问题
            chunk_size = min(file_size, 1024 * 1024)  # 最大1MB块

            # 执行多次覆写 (DoD 5220.22-M标准)
            with open(file_path, 'r+b') as f:
                # 第一次：用0覆写
                self._overwrite_file_chunks(f, file_size, chunk_size, b'\x00')

                # 第二次：用1覆写
                self._overwrite_file_chunks(f, file_size, chunk_size, b'\xFF')

                # 第三次：用随机数据覆写
                for offset in range(0, file_size, chunk_size):
                    f.seek(offset)
                    current_chunk_size = min(chunk_size, file_size - offset)
                    f.write(os.urandom(current_chunk_size))
                    f.flush()
                    os.fsync(f.fileno())

            # 重命名文件为随机名称
            dir_path = os.path.dirname(file_path)
            random_name = os.path.join(dir_path, f"tmp_{os.urandom(8).hex()}")
            os.rename(file_path, random_name)

            # 最后删除文件
            os.remove(random_name)

            return True
        except PermissionError:
            self.logger.error(f"权限不足，无法擦除文件: {file_path}")
            return False
        except OSError as e:
            self.logger.error(f"系统错误，无法擦除文件 {file_path}: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"安全擦除文件异常 {file_path}: {str(e)}")
            return False

    def _overwrite_file_chunks(self, file_obj, file_size, chunk_size, pattern_byte):
        """分块覆写文件"""
        pattern_chunk = pattern_byte * chunk_size
        for offset in range(0, file_size, chunk_size):
            file_obj.seek(offset)
            current_chunk_size = min(chunk_size, file_size - offset)
            if current_chunk_size < chunk_size:
                # 最后一块可能小于chunk_size
                pattern_chunk = pattern_byte * current_chunk_size
            file_obj.write(pattern_chunk[:current_chunk_size])
            file_obj.flush()
            os.fsync(file_obj.fileno())
