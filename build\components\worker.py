from PyQt5.QtCore import QThread, pyqtSignal
import time
import logging

class WorkerThread(QThread):
    """工作线程基类"""
    # 信号定义
    progressChanged = pyqtSignal(int)  # 进度变化信号
    statusChanged = pyqtSignal(str)    # 状态变化信号
    finished = pyqtSignal(bool, str)   # 完成信号(成功标志, 消息)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("CodePrivacy")
        self.is_running = False
        self.is_canceled = False
    
    def cancel(self):
        """取消操作"""
        self.is_canceled = True
        self.logger.info("操作已取消")
    
    def check_canceled(self):
        """检查是否已取消"""
        if self.is_canceled:
            self.finished.emit(False, "操作已取消")
            return True
        return False


class HWIdentifierWorker(WorkerThread):
    """硬件标识符工作线程"""
    def __init__(self, hw_manager, action, adapter=None):
        super().__init__()
        self.hw_manager = hw_manager
        self.action = action
        self.adapter = adapter
    
    def run(self):
        """执行操作"""
        self.is_running = True
        self.is_canceled = False
        
        try:
            if self.action == "change_mac":
                self.statusChanged.emit("正在更改MAC地址...")
                success, new_mac, message = self.hw_manager.change_mac_address(self.adapter)
                self.finished.emit(success, message)
            
            elif self.action == "change_uuid":
                self.statusChanged.emit("正在更改系统UUID...")
                success, new_uuid, message = self.hw_manager.change_system_uuid()
                self.finished.emit(success, message)
            
        except Exception as e:
            self.logger.error(f"硬件标识符操作异常: {str(e)}")
            self.finished.emit(False, f"操作失败: {str(e)}")
        
        finally:
            self.is_running = False


class VSCodeWorker(WorkerThread):
    """VSCode清理工作线程"""
    def __init__(self, vscode_cleaner, action):
        super().__init__()
        self.vscode_cleaner = vscode_cleaner
        self.action = action
    
    def run(self):
        """执行操作"""
        self.is_running = True
        self.is_canceled = False
        
        try:
            if self.action == "backup":
                self.statusChanged.emit("正在备份VSCode数据...")
                success, message = self.vscode_cleaner.backup_data()
                self.finished.emit(success, message)
            
            elif self.action == "clean_config":
                self.statusChanged.emit("正在清理VSCode配置...")
                self.progressChanged.emit(30)
                
                if self.check_canceled():
                    return
                    
                success, message = self.vscode_cleaner.clean_config()
                self.progressChanged.emit(100)
                self.finished.emit(success, message)
            
            elif self.action == "clean_cache":
                self.statusChanged.emit("正在清理VSCode缓存...")
                self.progressChanged.emit(30)
                
                if self.check_canceled():
                    return
                    
                success, message = self.vscode_cleaner.clean_cache()
                self.progressChanged.emit(100)
                self.finished.emit(success, message)
            
        except Exception as e:
            self.logger.error(f"VSCode清理操作异常: {str(e)}")
            self.finished.emit(False, f"操作失败: {str(e)}")
        
        finally:
            self.is_running = False


class TelemetryWorker(WorkerThread):
    """遥测管理工作线程"""
    def __init__(self, telemetry_manager, action):
        super().__init__()
        self.telemetry_manager = telemetry_manager
        self.action = action
    
    def run(self):
        """执行操作"""
        self.is_running = True
        self.is_canceled = False
        
        try:
            if self.action == "disable":
                self.statusChanged.emit("正在禁用遥测...")
                self.progressChanged.emit(30)
                
                if self.check_canceled():
                    return
                    
                success, message = self.telemetry_manager.disable_telemetry()
                self.progressChanged.emit(100)
                self.finished.emit(success, message)
            
            elif self.action == "new_id":
                self.statusChanged.emit("正在生成新的Machine ID...")
                self.progressChanged.emit(30)
                
                if self.check_canceled():
                    return
                    
                success, new_id, message = self.telemetry_manager.generate_new_id()
                self.progressChanged.emit(100)
                self.finished.emit(success, message)
            
        except Exception as e:
            self.logger.error(f"遥测管理操作异常: {str(e)}")
            self.finished.emit(False, f"操作失败: {str(e)}")
        
        finally:
            self.is_running = False


class BrowserWorker(WorkerThread):
    """浏览器保护工作线程"""
    def __init__(self, browser_manager, action, browser=None, options=None):
        super().__init__()
        self.browser_manager = browser_manager
        self.action = action
        self.browser = browser
        self.options = options or {}
    
    def run(self):
        """执行操作"""
        self.is_running = True
        self.is_canceled = False
        
        try:
            if self.action == "protect":
                self.statusChanged.emit(f"正在为 {self.browser} 应用保护...")
                self.progressChanged.emit(30)
                
                if self.check_canceled():
                    return
                
                canvas = self.options.get('canvas', True)
                webgl = self.options.get('webgl', True)
                headers = self.options.get('headers', True)
                
                success, message = self.browser_manager.apply_protection(
                    self.browser, canvas, webgl, headers
                )
                
                self.progressChanged.emit(100)
                self.finished.emit(success, message)
            
        except Exception as e:
            self.logger.error(f"浏览器保护操作异常: {str(e)}")
            self.finished.emit(False, f"操作失败: {str(e)}")
        
        finally:
            self.is_running = False


class SecureEraserWorker(WorkerThread):
    """安全擦除工作线程"""
    def __init__(self, eraser, action):
        super().__init__()
        self.eraser = eraser
        self.action = action
    
    def run(self):
        """执行操作"""
        self.is_running = True
        self.is_canceled = False
        
        try:
            if self.action == "scan":
                self.statusChanged.emit("正在扫描残留数据...")
                self.progressChanged.emit(30)
                
                if self.check_canceled():
                    return
                    
                success, message = self.eraser.scan_residual_data()
                self.progressChanged.emit(100)
                self.finished.emit(success, message)
            
            elif self.action == "erase":
                self.statusChanged.emit("正在安全擦除数据...")
                self.progressChanged.emit(30)
                
                if self.check_canceled():
                    return
                    
                success, message = self.eraser.erase_data()
                self.progressChanged.emit(100)
                self.finished.emit(success, message)
            
        except Exception as e:
            self.logger.error(f"安全擦除操作异常: {str(e)}")
            self.finished.emit(False, f"操作失败: {str(e)}")
        
        finally:
            self.is_running = False


class RotatorWorker(WorkerThread):
    """参数轮换工作线程"""
    def __init__(self, rotator, action, interval=None):
        super().__init__()
        self.rotator = rotator
        self.action = action
        self.interval = interval
    
    def run(self):
        """执行操作"""
        self.is_running = True
        self.is_canceled = False
        
        try:
            if self.action == "start":
                self.statusChanged.emit("正在启动参数轮换...")
                success, message = self.rotator.start_rotation(self.interval)
                self.finished.emit(success, message)
            
            elif self.action == "stop":
                self.statusChanged.emit("正在停止参数轮换...")
                success, message = self.rotator.stop_rotation()
                self.finished.emit(success, message)
            
        except Exception as e:
            self.logger.error(f"参数轮换操作异常: {str(e)}")
            self.finished.emit(False, f"操作失败: {str(e)}")
        
        finally:
            self.is_running = False
