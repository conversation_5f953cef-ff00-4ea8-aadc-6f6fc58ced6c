CodePrivacy - 开发环境隐私保护工具
=====================================

版本: 1.0.0
构建日期: 2024年

## 简介

CodePrivacy 是一个专为开发者设计的隐私保护工具，帮助保护开发环境中的隐私信息，
防止个人信息泄露和指纹追踪。

## 主要功能

1. **VSCode 配置清理**
   - 清理 VSCode 的机器标识符
   - 清理工作区存储和全局存储
   - 备份和恢复配置

2. **浏览器指纹保护**
   - Chrome/Edge/Firefox 指纹保护
   - Canvas 和 WebGL 指纹防护
   - 用户代理字符串保护

3. **硬件标识符管理**
   - MAC 地址修改
   - 系统 UUID 管理
   - 网络适配器信息保护

4. **系统参数轮换**
   - 自动轮换系统标识符
   - 定时任务支持
   - 参数验证和恢复

5. **安全文件擦除**
   - 军用级文件擦除 (DoD 5220.22-M)
   - 多次覆写确保数据安全
   - 支持文件和目录擦除

## 使用说明

### 启动程序
1. 双击 CodePrivacy.exe 启动程序
2. **强烈建议以管理员身份运行**以获得完整功能
3. 首次运行会在用户目录创建配置和日志文件

### 权限要求
- 某些功能需要管理员权限（如 MAC 地址修改）
- 在 Windows 上右键选择"以管理员身份运行"
- 程序会自动检测权限并提示用户

### 配置文件位置
- Windows: %APPDATA%\CodePrivacy\
- macOS: ~/Library/Application Support/CodePrivacy/
- Linux: ~/.config/CodePrivacy/

## 注意事项

⚠️ **重要警告**
- 使用前请备份重要数据和配置
- 某些操作不可逆，请谨慎使用
- 建议在测试环境中先试用

🔒 **安全提示**
- 程序会记录操作日志，请定期检查
- 敏感操作会要求用户确认
- 建议定期更新程序版本

🛠️ **故障排除**
- 如程序无法启动，请检查是否有杀毒软件阻止
- 查看日志文件了解详细错误信息
- 确保有足够的磁盘空间和权限

## 技术支持

### 日志文件
程序运行时会生成详细的日志文件，位于配置目录的 logs 子目录中。
如遇问题，请查看最新的日志文件。

### 常见问题
1. **程序启动后没有窗口**
   - 检查任务管理器中是否有 CodePrivacy.exe 进程
   - 尝试以管理员身份运行
   - 检查防病毒软件是否阻止了程序

2. **某些功能不可用**
   - 确保以管理员身份运行
   - 检查系统兼容性
   - 查看日志文件了解具体错误

3. **操作失败**
   - 检查目标文件/程序是否正在使用
   - 确保有足够的权限
   - 查看错误提示和日志信息

### 系统要求
- 操作系统: Windows 10/11, macOS 10.14+, Linux (Ubuntu 18.04+)
- 内存: 至少 512MB 可用内存
- 磁盘: 至少 100MB 可用空间
- 权限: 建议管理员权限

## 免责声明

本软件仅供学习和研究使用。使用者应当：
- 遵守当地法律法规
- 不得用于非法用途
- 自行承担使用风险
- 使用前备份重要数据

开发团队不对因使用本软件造成的任何损失承担责任。

## 版权信息

Copyright © 2024 CodePrivacy Team
本软件按"现状"提供，不提供任何明示或暗示的保证。

---
如有问题或建议，请联系开发团队。
