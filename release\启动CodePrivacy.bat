@echo off
chcp 65001 >nul
title CodePrivacy 启动器

echo.
echo   ╔══════════════════════════════════════╗
echo   ║          CodePrivacy 启动器          ║
echo   ║      开发环境隐私保护工具            ║
echo   ╚══════════════════════════════════════╝
echo.

echo 正在检查程序文件...
if not exist "CodePrivacy.exe" (
    echo ✗ 错误: 未找到 CodePrivacy.exe
    echo 请确保此批处理文件与 CodePrivacy.exe 在同一目录中
    echo.
    pause
    exit /b 1
)

echo ✓ 找到程序文件: CodePrivacy.exe
echo.

echo 选择启动方式:
echo [1] 普通模式启动
echo [2] 管理员模式启动 (推荐)
echo [3] 查看帮助信息
echo [4] 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo.
    echo 正在以普通模式启动 CodePrivacy...
    echo 注意: 某些功能可能需要管理员权限
    echo.
    start "" "CodePrivacy.exe"
    echo 程序已启动。如果没有看到窗口，请检查任务管理器。
    timeout /t 3 >nul
    exit /b 0
)

if "%choice%"=="2" (
    echo.
    echo 正在以管理员模式启动 CodePrivacy...
    echo 系统可能会弹出 UAC 权限确认对话框，请点击"是"
    echo.
    
    :: 使用 PowerShell 以管理员身份启动
    powershell -Command "Start-Process -FilePath '%~dp0CodePrivacy.exe' -Verb RunAs"
    
    if %errorlevel% equ 0 (
        echo ✓ 程序已以管理员权限启动
    ) else (
        echo ✗ 启动失败，可能是用户取消了权限确认
    )
    
    timeout /t 3 >nul
    exit /b 0
)

if "%choice%"=="3" (
    echo.
    echo ═══════════════════════════════════════
    echo              帮助信息
    echo ═══════════════════════════════════════
    echo.
    echo CodePrivacy 是一个开发环境隐私保护工具，主要功能包括:
    echo.
    echo • VSCode 配置清理和机器标识符管理
    echo • 浏览器指纹保护 (Chrome/Firefox/Edge)
    echo • 硬件标识符管理 (MAC地址等)
    echo • 系统参数轮换和自动化
    echo • 安全文件擦除功能
    echo.
    echo 使用建议:
    echo • 首次使用建议以管理员身份运行
    echo • 使用前请备份重要数据和配置
    echo • 查看 README.txt 了解详细使用说明
    echo.
    echo 配置文件位置: %%APPDATA%%\CodePrivacy\
    echo 日志文件位置: %%APPDATA%%\CodePrivacy\logs\
    echo.
    echo ═══════════════════════════════════════
    echo.
    pause
    goto :start
)

if "%choice%"=="4" (
    echo.
    echo 感谢使用 CodePrivacy！
    timeout /t 2 >nul
    exit /b 0
)

echo.
echo ✗ 无效选择，请输入 1-4 之间的数字
timeout /t 2 >nul
goto :start

:start
cls
goto :eof
