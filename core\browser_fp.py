import os
import json
import platform
import logging
import shutil

class BrowserFingerprintManager:
    """浏览器指纹管理类"""
    def __init__(self):
        self.logger = logging.getLogger("CodePrivacy")
        self.browser_paths = self._get_browser_paths()
    
    def _get_browser_paths(self):
        """获取浏览器路径"""
        system = platform.system()
        paths = {}
        
        if system == "Windows":
            appdata = os.environ.get("APPDATA", "")
            localappdata = os.environ.get("LOCALAPPDATA", "")
            
            paths["Chrome"] = {
                "extensions": os.path.join(localappdata, "Google", "Chrome", "User Data", "Default", "Extensions"),
                "preferences": os.path.join(localappdata, "Google", "Chrome", "User Data", "Default", "Preferences")
            }
            
            paths["Firefox"] = {
                "extensions": os.path.join(appdata, "Mozilla", "Firefox", "Profiles"),
                "preferences": None  # Firefox使用profiles.ini和多个配置文件
            }
            
            paths["Edge"] = {
                "extensions": os.path.join(localappdata, "Microsoft", "Edge", "User Data", "Default", "Extensions"),
                "preferences": os.path.join(localappdata, "Microsoft", "Edge", "User Data", "Default", "Preferences")
            }
            
            paths["Opera"] = {
                "extensions": os.path.join(appdata, "Opera Software", "Opera Stable", "Extensions"),
                "preferences": os.path.join(appdata, "Opera Software", "Opera Stable", "Preferences")
            }
        
        elif system == "Darwin":  # macOS
            home = os.path.expanduser("~")
            
            paths["Chrome"] = {
                "extensions": os.path.join(home, "Library", "Application Support", "Google", "Chrome", "Default", "Extensions"),
                "preferences": os.path.join(home, "Library", "Application Support", "Google", "Chrome", "Default", "Preferences")
            }
            
            paths["Firefox"] = {
                "extensions": os.path.join(home, "Library", "Application Support", "Firefox", "Profiles"),
                "preferences": None
            }
            
            paths["Edge"] = {
                "extensions": os.path.join(home, "Library", "Application Support", "Microsoft Edge", "Default", "Extensions"),
                "preferences": os.path.join(home, "Library", "Application Support", "Microsoft Edge", "Default", "Preferences")
            }
            
            paths["Opera"] = {
                "extensions": os.path.join(home, "Library", "Application Support", "com.operasoftware.Opera", "Extensions"),
                "preferences": os.path.join(home, "Library", "Application Support", "com.operasoftware.Opera", "Preferences")
            }
        
        else:  # Linux
            home = os.path.expanduser("~")
            
            paths["Chrome"] = {
                "extensions": os.path.join(home, ".config", "google-chrome", "Default", "Extensions"),
                "preferences": os.path.join(home, ".config", "google-chrome", "Default", "Preferences")
            }
            
            paths["Firefox"] = {
                "extensions": os.path.join(home, ".mozilla", "firefox"),
                "preferences": None
            }
            
            paths["Edge"] = {
                "extensions": os.path.join(home, ".config", "microsoft-edge", "Default", "Extensions"),
                "preferences": os.path.join(home, ".config", "microsoft-edge", "Default", "Preferences")
            }
            
            paths["Opera"] = {
                "extensions": os.path.join(home, ".config", "opera", "Extensions"),
                "preferences": os.path.join(home, ".config", "opera", "Preferences")
            }
        
        return paths
    
    def apply_protection(self, browser, canvas=True, webgl=True, headers=True):
        """应用浏览器保护"""
        try:
            if browser not in self.browser_paths:
                return False, f"不支持的浏览器: {browser}"
            
            browser_path = self.browser_paths[browser]
            
            # 修改浏览器偏好设置
            if browser in ["Chrome", "Edge", "Opera"] and browser_path["preferences"]:
                self._apply_chromium_protection(browser_path["preferences"], canvas, webgl, headers)
            elif browser == "Firefox":
                self._apply_firefox_protection(browser_path["extensions"], canvas, webgl, headers)
            
            return True, f"{browser}浏览器保护已应用"
        except Exception as e:
            self.logger.error(f"应用浏览器保护异常: {str(e)}")
            return False, f"应用保护失败: {str(e)}"
    
    def _apply_chromium_protection(self, prefs_path, canvas, webgl, headers):
        """应用Chromium系浏览器保护"""
        if not os.path.exists(prefs_path):
            return
        
        try:
            # 读取现有配置
            with open(prefs_path, 'r') as f:
                prefs = json.load(f)
            
            # 备份当前配置
            backup_path = f"{prefs_path}.backup"
            with open(backup_path, 'w') as f:
                json.dump(prefs, f, indent=4)
            
            # 应用Canvas保护
            if canvas:
                if "profile" not in prefs:
                    prefs["profile"] = {}
                if "content_settings" not in prefs["profile"]:
                    prefs["profile"]["content_settings"] = {}
                if "exceptions" not in prefs["profile"]["content_settings"]:
                    prefs["profile"]["content_settings"]["exceptions"] = {}
                
                # 禁用Canvas读取
                prefs["profile"]["content_settings"]["exceptions"]["plugins"] = {
                    "canvas_fingerprinting": {"setting": 2}
                }
            
            # 应用WebGL保护
            if webgl:
                if "hardware_acceleration_mode_enabled" in prefs:
                    prefs["hardware_acceleration_mode_enabled"] = False
                
                if "browser" not in prefs:
                    prefs["browser"] = {}
                
                prefs["browser"]["enable_features"] = prefs["browser"].get("enable_features", "") + ",DisableWebGL"
            
            # 应用HTTP请求头保护
            if headers:
                if "session" not in prefs:
                    prefs["session"] = {}
                if "user_agent" not in prefs["session"]:
                    prefs["session"]["user_agent"] = ""
                
                # 使用通用User-Agent
                prefs["session"]["user_agent"] = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                
                # 禁用额外头信息
                if "enable_do_not_track" not in prefs:
                    prefs["enable_do_not_track"] = True
            
            # 保存修改后的配置
            with open(prefs_path, 'w') as f:
                json.dump(prefs, f, indent=4)
        
        except Exception as e:
            self.logger.error(f"应用Chromium保护异常: {str(e)}")
            raise
    
    def _apply_firefox_protection(self, profiles_path, canvas, webgl, headers):
        """应用Firefox浏览器保护"""
        if not os.path.exists(profiles_path):
            return
        
        try:
            # 查找默认配置文件夹
            profile_dir = None
            for item in os.listdir(profiles_path):
                if item.endswith(".default") or "default" in item:
                    profile_dir = os.path.join(profiles_path, item)
                    break
            
            if not profile_dir:
                return
            
            # 修改user.js文件
            user_js_path = os.path.join(profile_dir, "user.js")
            
            # 创建或追加到user.js
            with open(user_js_path, 'a+') as f:
                # Canvas保护
                if canvas:
                    f.write('user_pref("privacy.resistFingerprinting", true);\n')
                    f.write('user_pref("privacy.trackingprotection.enabled", true);\n')
                
                # WebGL保护
                if webgl:
                    f.write('user_pref("webgl.disabled", true);\n')
                    f.write('user_pref("webgl.enable-webgl2", false);\n')
                
                # HTTP请求头保护
                if headers:
                    f.write('user_pref("general.useragent.override", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0");\n')
                    f.write('user_pref("privacy.donottrackheader.enabled", true);\n')
        
        except Exception as e:
            self.logger.error(f"应用Firefox保护异常: {str(e)}")
            raise
    
    def verify_protection(self, browser):
        """验证浏览器保护状态"""
        try:
            if browser not in self.browser_paths:
                return f"不支持的浏览器: {browser}"
            
            browser_path = self.browser_paths[browser]
            results = []
            
            if browser in ["Chrome", "Edge", "Opera"] and browser_path["preferences"]:
                # 检查Chromium浏览器
                if os.path.exists(browser_path["preferences"]):
                    with open(browser_path["preferences"], 'r') as f:
                        prefs = json.load(f)
                    
                    # 验证Canvas保护
                    canvas_protected = False
                    try:
                        if prefs.get("profile", {}).get("content_settings", {}).get("exceptions", {}).get("plugins", {}).get("canvas_fingerprinting", {}).get("setting") == 2:
                            canvas_protected = True
                    except:
                        pass
                    results.append(f"Canvas保护: {'已启用' if canvas_protected else '未启用'}")
                    
                    # 验证WebGL保护
                    webgl_protected = False
                    if not prefs.get("hardware_acceleration_mode_enabled", True) or "DisableWebGL" in prefs.get("browser", {}).get("enable_features", ""):
                        webgl_protected = True
                    results.append(f"WebGL保护: {'已启用' if webgl_protected else '未启用'}")
                    
                    # 验证HTTP请求头保护
                    headers_protected = False
                    if prefs.get("enable_do_not_track") and prefs.get("session", {}).get("user_agent"):
                        headers_protected = True
                    results.append(f"HTTP请求头保护: {'已启用' if headers_protected else '未启用'}")
                else:
                    results.append("未找到浏览器配置文件")
            
            elif browser == "Firefox":
                # 检查Firefox浏览器
                profile_dir = None
                for item in os.listdir(browser_path["extensions"]):
                    if item.endswith(".default") or "default" in item:
                        profile_dir = os.path.join(browser_path["extensions"], item)
                        break
                
                if profile_dir:
                    user_js_path = os.path.join(profile_dir, "user.js")
                    if os.path.exists(user_js_path):
                        with open(user_js_path, 'r') as f:
                            content = f.read()
                        
                        # 验证各项保护
                        canvas_protected = "privacy.resistFingerprinting" in content
                        results.append(f"Canvas保护: {'已启用' if canvas_protected else '未启用'}")
                        
                        webgl_protected = "webgl.disabled" in content
                        results.append(f"WebGL保护: {'已启用' if webgl_protected else '未启用'}")
                        
                        headers_protected = "general.useragent.override" in content
                        results.append(f"HTTP请求头保护: {'已启用' if headers_protected else '未启用'}")
                    else:
                        results.append("未找到user.js配置文件")
                else:
                    results.append("未找到Firefox配置目录")
            
            return "\n".join(results)
        except Exception as e:
            return f"验证保护状态失败: {str(e)}"
