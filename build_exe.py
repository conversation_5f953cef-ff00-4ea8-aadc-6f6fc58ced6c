#!/usr/bin/env python3
"""
CodePrivacy 打包脚本
使用 PyInstaller 将项目打包成可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """检查打包要求"""
    print("检查打包要求...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller 未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装完成")
    
    # 检查PyQt5
    try:
        import PyQt5
        print(f"✓ PyQt5 已安装")
    except ImportError:
        print("✗ PyQt5 未安装，请先安装依赖项")
        return False
    
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('core', 'core'),
        ('utils', 'utils'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'logging.handlers',
        'json',
        'platform',
        'subprocess',
        'threading',
        'time',
        'os',
        'sys',
        'ctypes',
        'winreg',
        'psutil',
        'cryptography',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CodePrivacy',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version='version_info.txt'  # 可以添加版本信息文件
)
'''
    
    with open('CodePrivacy.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建了 CodePrivacy.spec 文件")

def create_version_info():
    """创建版本信息文件"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'CodePrivacy Team'),
        StringStruct(u'FileDescription', u'CodePrivacy - 开发环境隐私保护工具'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'CodePrivacy'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024'),
        StringStruct(u'OriginalFilename', u'CodePrivacy.exe'),
        StringStruct(u'ProductName', u'CodePrivacy'),
        StringStruct(u'ProductVersion', u'*******')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✓ 创建了版本信息文件")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 清理之前的构建
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("✓ 清理了 build 目录")
        
        if os.path.exists('dist'):
            shutil.rmtree('dist')
            print("✓ 清理了 dist 目录")
        
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "CodePrivacy.spec"]
        
        print("执行构建命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 构建成功!")
            
            # 检查输出文件
            exe_path = os.path.join('dist', 'CodePrivacy.exe')
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"✓ 可执行文件: {exe_path} ({size:.1f} MB)")
                return True
            else:
                print("✗ 未找到生成的可执行文件")
                return False
        else:
            print("✗ 构建失败:")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 构建过程中出现异常: {str(e)}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("创建便携版包...")
    
    try:
        # 创建便携版目录
        portable_dir = "CodePrivacy_Portable"
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        
        os.makedirs(portable_dir)
        
        # 复制可执行文件
        exe_src = os.path.join('dist', 'CodePrivacy.exe')
        exe_dst = os.path.join(portable_dir, 'CodePrivacy.exe')
        
        if os.path.exists(exe_src):
            shutil.copy2(exe_src, exe_dst)
            print(f"✓ 复制可执行文件到 {portable_dir}")
        
        # 复制说明文件
        readme_content = """# CodePrivacy 便携版

## 使用说明

1. 双击 CodePrivacy.exe 启动程序
2. 首次运行建议以管理员身份启动以获得完整功能
3. 程序会在用户目录下创建配置和日志文件

## 功能特性

- VSCode 配置清理
- 浏览器指纹保护  
- 硬件标识符管理
- 系统参数轮换
- 安全文件擦除

## 注意事项

- 某些功能需要管理员权限
- 建议在使用前备份重要数据
- 详细使用说明请参考项目文档

## 技术支持

如有问题请查看日志文件或联系开发团队。
"""
        
        with open(os.path.join(portable_dir, 'README.txt'), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"✓ 便携版包创建完成: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"✗ 创建便携版包失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("CodePrivacy 打包工具")
    print("=" * 50)
    
    # 检查要求
    if not check_requirements():
        print("✗ 要求检查失败")
        return 1
    
    # 创建配置文件
    create_spec_file()
    create_version_info()
    
    # 构建可执行文件
    if not build_executable():
        print("✗ 构建失败")
        return 1
    
    # 创建便携版包
    if not create_portable_package():
        print("✗ 便携版包创建失败")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 打包完成!")
    print("📁 可执行文件: dist/CodePrivacy.exe")
    print("📦 便携版包: CodePrivacy_Portable/")
    print("\n建议以管理员身份运行以获得完整功能。")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
