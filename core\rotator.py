import os
import uuid
import random
import platform
import logging
import threading
import time
import json

class SystemParamRotator:
    """系统参数轮换类"""
    def __init__(self):
        self.logger = logging.getLogger("CodePrivacy")
        self.rotation_active = False
        self.rotation_thread = None
        self.rotation_interval = 3600  # 默认1小时
        self.stop_event = threading.Event()
        self.config_paths = self._get_config_paths()
    
    def _get_config_paths(self):
        """获取配置文件路径"""
        system = platform.system()
        paths = {}
        
        if system == "Windows":
            appdata = os.environ.get("APPDATA", "")
            paths["vscode"] = os.path.join(appdata, "Code", "User", "settings.json")
            paths["machine_id"] = os.path.join(appdata, "Code", "machineid")
        elif system == "Darwin":  # macOS
            home = os.path.expanduser("~")
            paths["vscode"] = os.path.join(home, "Library", "Application Support", "Code", "User", "settings.json")
            paths["machine_id"] = os.path.join(home, "Library", "Application Support", "Code", "machineid")
        else:  # Linux
            home = os.path.expanduser("~")
            paths["vscode"] = os.path.join(home, ".config", "Code", "User", "settings.json")
            paths["machine_id"] = os.path.join(home, ".config", "Code", "machineid")
        
        return paths
    
    def start_rotation(self, interval=3600):
        """启动系统参数轮换"""
        if self.rotation_active:
            return False, "轮换已经在运行中"
        
        try:
            self.rotation_interval = interval
            self.stop_event.clear()
            self.rotation_active = True
            
            self.rotation_thread = threading.Thread(target=self._rotation_loop)
            self.rotation_thread.daemon = True
            self.rotation_thread.start()
            
            return True, f"系统参数轮换已启动，间隔: {interval}秒"
        except Exception as e:
            self.logger.error(f"启动轮换异常: {str(e)}")
            self.rotation_active = False
            return False, f"启动轮换失败: {str(e)}"
    
    def stop_rotation(self):
        """停止系统参数轮换"""
        if not self.rotation_active:
            return False, "轮换未运行"
        
        try:
            self.stop_event.set()
            if self.rotation_thread:
                self.rotation_thread.join(timeout=5)
            
            self.rotation_active = False
            return True, "系统参数轮换已停止"
        except Exception as e:
            self.logger.error(f"停止轮换异常: {str(e)}")
            return False, f"停止轮换失败: {str(e)}"
    
    def _rotation_loop(self):
        """轮换循环"""
        while not self.stop_event.is_set():
            try:
                # 轮换Machine ID
                self._rotate_machine_id()
                
                # 轮换浏览器User-Agent
                self._rotate_user_agent()
                
                # 其他轮换...
                
                # 等待下一次轮换
                self.stop_event.wait(self.rotation_interval)
            except Exception as e:
                self.logger.error(f"轮换异常: {str(e)}")
                time.sleep(60)  # 发生错误时等待一分钟后重试
    
    def _rotate_machine_id(self):
        """轮换Machine ID"""
        try:
            machine_id_path = self.config_paths["machine_id"]
            
            if not os.path.exists(machine_id_path):
                return
            
            # 生成新的UUID
            new_id = str(uuid.uuid4())
            
            # 保存新ID
            with open(machine_id_path, 'w') as f:
                f.write(new_id)
            
            self.logger.info(f"已轮换Machine ID: {new_id}")
        except Exception as e:
            self.logger.error(f"轮换Machine ID异常: {str(e)}")
    
    def _rotate_user_agent(self):
        """轮换浏览器User-Agent"""
        try:
            # 常见User-Agent列表
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
            ]

            # 随机选择一个User-Agent
            new_agent = random.choice(user_agents)

            # 更新Chromium系浏览器的User-Agent
            self._update_chromium_user_agent(new_agent)
            return True, f"用户代理已更改为: {new_agent}"
        except Exception as e:
            self.logger.error(f"轮换用户代理失败: {str(e)}")
            return False, f"轮换失败: {str(e)}"

    
    def _update_chromium_user_agent(self, user_agent):
        """更新Chromium系浏览器的User-Agent"""
        try:
            system = platform.system()
            
            if system == "Windows":
                # 查找Chrome、Edge等浏览器的Preferences文件
                browsers = {
                    "Chrome": os.path.join(os.environ.get("LOCALAPPDATA", ""), "Google", "Chrome", "User Data", "Default", "Preferences"),
                    "Edge": os.path.join(os.environ.get("LOCALAPPDATA", ""), "Microsoft", "Edge", "User Data", "Default", "Preferences")
                }
            elif system == "Darwin":  # macOS
                home = os.path.expanduser("~")
                browsers = {
                    "Chrome": os.path.join(home, "Library", "Application Support", "Google", "Chrome", "Default", "Preferences"),
                    "Edge": os.path.join(home, "Library", "Application Support", "Microsoft Edge", "Default", "Preferences")
                }
            else:  # Linux
                home = os.path.expanduser("~")
                browsers = {
                    "Chrome": os.path.join(home, ".config", "google-chrome", "Default", "Preferences"),
                    "Edge": os.path.join(home, ".config", "microsoft-edge", "Default", "Preferences")
                }
            
            # 更新各个浏览器的User-Agent
            for browser, prefs_path in browsers.items():
                if os.path.exists(prefs_path):
                    try:
                        # 读取当前配置
                        with open(prefs_path, 'r') as f:
                            prefs = json.load(f)
                        
                        # 更新User-Agent
                        if "session" not in prefs:
                            prefs["session"] = {}
                        
                        prefs["session"]["user_agent"] = user_agent
                        
                        # 保存更新后的配置
                        with open(prefs_path, 'w') as f:
                            json.dump(prefs, f, indent=4)
                        
                        self.logger.info(f"已更新{browser}浏览器的User-Agent")
                    except Exception as e:
                        self.logger.error(f"更新{browser} User-Agent异常: {str(e)}")
        
        except Exception as e:
            self.logger.error(f"更新Chromium User-Agent异常: {str(e)}")
            
    def verify_rotation(self):
        """验证轮换状态"""
        try:
            status = []
            status.append(f"轮换状态: {'运行中' if self.rotation_active else '未运行'}")
            if self.rotation_active:
                status.append(f"轮换间隔: {self.rotation_interval}秒")
            
            # 检查Machine ID
            machine_id_path = self.config_paths["machine_id"]
            if os.path.exists(machine_id_path):
                with open(machine_id_path, 'r') as f:
                    current_id = f.read().strip()
                status.append(f"当前Machine ID: {current_id}")
            
            # 可以添加更多状态检查...
            
            return "\n".join(status)
        except Exception as e:
            return f"验证轮换状态失败: {str(e)}"
