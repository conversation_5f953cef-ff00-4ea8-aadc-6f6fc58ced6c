"""
配置管理模块
"""

import os
import json
import platform
from pathlib import Path
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = self._get_config_dir()
        self.config_file = os.path.join(self.config_dir, "config.json")
        self.default_config = self._get_default_config()
        self.config = self._load_config()
    
    def _get_config_dir(self) -> str:
        """获取配置目录"""
        system = platform.system()
        if system == "Windows":
            config_dir = os.path.join(os.environ.get("APPDATA", ""), "CodePrivacy")
        elif system == "Darwin":  # macOS
            config_dir = os.path.expanduser("~/Library/Application Support/CodePrivacy")
        else:  # Linux
            config_dir = os.path.expanduser("~/.config/CodePrivacy")
        
        os.makedirs(config_dir, exist_ok=True)
        return config_dir
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "general": {
                "auto_backup": True,
                "confirm_destructive_operations": True,
                "log_level": "INFO",
                "check_updates": True
            },
            "vscode": {
                "backup_before_clean": True,
                "clean_extensions": False,
                "clean_workspace_storage": True,
                "clean_global_storage": True
            },
            "browser": {
                "default_browser": "Chrome",
                "apply_canvas_protection": True,
                "apply_webgl_protection": True,
                "apply_header_protection": True,
                "backup_browser_config": True
            },
            "hardware": {
                "require_admin_for_mac_change": True,
                "verify_changes": True,
                "backup_original_values": True
            },
            "rotation": {
                "default_interval": 3600,
                "auto_start": False,
                "rotate_machine_id": True,
                "rotate_user_agent": True
            },
            "security": {
                "secure_erase_passes": 3,
                "verify_erase": True,
                "log_security_events": True
            }
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置以确保所有键都存在
                return self._merge_configs(self.default_config, config)
            except (json.JSONDecodeError, IOError):
                # 配置文件损坏，使用默认配置
                return self.default_config.copy()
        else:
            # 配置文件不存在，创建默认配置
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的键"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值，支持点号分隔的键"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """保存配置到文件"""
        try:
            config_to_save = config if config is not None else self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=4, ensure_ascii=False)
            return True
        except IOError:
            return False
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        self.config = self.default_config.copy()
        self.save_config()
    
    def export_config(self, file_path: str) -> bool:
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except IOError:
            return False
    
    def import_config(self, file_path: str) -> bool:
        """从指定文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 验证配置格式
            if self._validate_config(imported_config):
                self.config = self._merge_configs(self.default_config, imported_config)
                self.save_config()
                return True
            else:
                return False
        except (json.JSONDecodeError, IOError):
            return False
    
    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置格式"""
        # 简单验证：检查是否包含必要的顶级键
        required_keys = ["general", "vscode", "browser", "hardware", "rotation", "security"]
        return all(key in config for key in required_keys)
    
    def get_backup_dir(self) -> str:
        """获取备份目录"""
        backup_dir = os.path.join(self.config_dir, "backups")
        os.makedirs(backup_dir, exist_ok=True)
        return backup_dir
    
    def get_log_dir(self) -> str:
        """获取日志目录"""
        log_dir = os.path.join(self.config_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)
        return log_dir

# 全局配置实例
config_manager = ConfigManager()

def get_config(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return config_manager.get(key, default)

def set_config(key: str, value: Any) -> None:
    """设置配置值的便捷函数"""
    config_manager.set(key, value)
    config_manager.save_config()

def get_backup_dir() -> str:
    """获取备份目录的便捷函数"""
    return config_manager.get_backup_dir()

def get_log_dir() -> str:
    """获取日志目录的便捷函数"""
    return config_manager.get_log_dir()
