import sys
import os
import platform
import logging
import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QTabWidget, 
                            QMessageBox, QProgressBar, QGroupBox, QTextEdit, 
                            QCheckBox, QSpinBox, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer

# 导入核心模块
from core.vscode_cleaner import VSCodeCleaner
from core.telemetry import TelemetryManager
from core.browser_fp import BrowserFingerprintManager
from core.hw_identifiers import HardwareIdentifierManager
from core.rotator import SystemParamRotator
from core.secure_eraser import SecureEraser
from utils.logger import setup_logger

class WorkerThread(QThread):
    """工作线程基类，用于处理耗时操作"""
    update_progress = pyqtSignal(int)
    update_status = pyqtSignal(str)
    operation_complete = pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("CodePrivacy")

class VSCodeCleanerThread(WorkerThread):
    """VSCode清理线程"""
    def __init__(self, cleaner, clean_config=True, clean_cache=True, backup=True):
        super().__init__()
        self.cleaner = cleaner
        self.clean_config = clean_config
        self.clean_cache = clean_cache
        self.backup = backup
    
    def run(self):
        try:
            if self.backup:
                self.update_status.emit("正在备份VSCode数据...")
                success, message = self.cleaner.backup_data()
                self.logger.info(f"备份结果: {message}")
                self.update_progress.emit(30)
            
            if self.clean_config:
                self.update_status.emit("正在清理VSCode配置...")
                success, message = self.cleaner.clean_config()
                self.logger.info(f"配置清理结果: {message}")
                self.update_progress.emit(60)
            
            if self.clean_cache:
                self.update_status.emit("正在清理VSCode缓存...")
                success, message = self.cleaner.clean_cache()
                self.logger.info(f"缓存清理结果: {message}")
                self.update_progress.emit(90)
            
            self.update_progress.emit(100)
            self.operation_complete.emit(True, "VSCode清理完成")
        except Exception as e:
            self.logger.error(f"VSCode清理异常: {str(e)}")
            self.operation_complete.emit(False, f"错误: {str(e)}")

class HardwareIDThread(WorkerThread):
    """硬件标识符修改线程"""
    def __init__(self, hw_manager, change_mac=False, change_uuid=False, adapter_name=None):
        super().__init__()
        self.hw_manager = hw_manager
        self.change_mac = change_mac
        self.change_uuid = change_uuid
        self.adapter_name = adapter_name
    
    def run(self):
        try:
            if self.change_mac:
                self.update_status.emit("正在获取当前MAC地址...")
                old_mac = self.hw_manager.get_current_mac(self.adapter_name)
                self.update_progress.emit(20)
                
                self.update_status.emit("正在修改MAC地址...")
                success, new_mac, message = self.hw_manager.change_mac_address(self.adapter_name)
                
                if success:
                    log_msg = f"MAC地址修改: {old_mac} -> {new_mac}"
                    self.logger.info(log_msg)
                    self.update_status.emit(log_msg)
                else:
                    self.logger.error(f"MAC地址修改失败: {message}")
                    self.update_status.emit(f"MAC地址修改失败: {message}")
                self.update_progress.emit(60)
            
            if self.change_uuid:
                self.update_status.emit("正在获取当前UUID...")
                old_uuid = self.hw_manager.get_current_uuid()
                self.update_progress.emit(70)
                
                self.update_status.emit("正在修改系统UUID...")
                success, new_uuid, message = self.hw_manager.change_system_uuid()
                
                if success:
                    log_msg = f"UUID修改: {old_uuid} -> {new_uuid}"
                    self.logger.info(log_msg)
                    self.update_status.emit(log_msg)
                else:
                    self.logger.error(f"UUID修改失败: {message}")
                    self.update_status.emit(f"UUID修改失败: {message}")
                
                self.update_progress.emit(100)
            
            self.operation_complete.emit(True, "硬件标识符修改完成")
        except Exception as e:
            self.logger.error(f"硬件标识符修改异常: {str(e)}")
            self.operation_complete.emit(False, f"错误: {str(e)}")

class SecureEraseThread(WorkerThread):
    """安全擦除线程"""
    def __init__(self, eraser):
        super().__init__()
        self.eraser = eraser
    
    def run(self):
        try:
            self.update_status.emit("正在扫描残留数据...")
            scan_success, scan_result = self.eraser.scan_residual_data()
            self.logger.info(f"扫描结果: {scan_result}")
            self.update_progress.emit(30)
            
            self.update_status.emit("正在安全擦除数据...")
            erase_success, erase_result = self.eraser.erase_data()
            self.logger.info(f"擦除结果: {erase_result}")
            self.update_progress.emit(100)
            
            self.operation_complete.emit(True, f"扫描结果: {scan_result}\n擦除结果: {erase_result}")
        except Exception as e:
            self.logger.error(f"安全擦除异常: {str(e)}")
            self.operation_complete.emit(False, f"错误: {str(e)}")

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.logger = setup_logger()
        self.logger.info("CodePrivacy启动")
        
        # 初始化核心模块
        self.vscode_cleaner = VSCodeCleaner()
        self.telemetry_manager = TelemetryManager()
        self.browser_fp_manager = BrowserFingerprintManager()
        self.hw_identifier_manager = HardwareIdentifierManager()
        self.rotator = SystemParamRotator()
        self.secure_eraser = SecureEraser()
        
        # 初始化UI
        self.init_ui()
        
        # 检查权限
        self.check_admin_privileges()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("CodePrivacy - 开发环境隐私保护工具")
        self.setMinimumSize(800, 600)
        
        # 中央部件
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QVBoxLayout(central)
        
        # 选项卡
        tabs = QTabWidget()
        main_layout.addWidget(tabs)
        
        # 状态部分
        status_group = QGroupBox("状态与日志")
        status_layout = QVBoxLayout(status_group)
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.progress_bar)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        status_layout.addWidget(self.log_display)
        
        main_layout.addWidget(status_group)
        
        # 创建选项卡
        self.create_vscode_tab(tabs)
        self.create_telemetry_tab(tabs)
        self.create_browser_tab(tabs)
        self.create_hardware_tab(tabs)
        self.create_rotator_tab(tabs)
        self.create_eraser_tab(tabs)
        
        # 设置日志处理器
        log_handler = LogHandler(self.log_display)
        self.logger.addHandler(log_handler)
        
        # 定时刷新状态
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_status)
        self.refresh_timer.start(5000)  # 每5秒刷新一次
    
    def create_vscode_tab(self, tabs):
        """创建VSCode选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 选项
        options_group = QGroupBox("选项")
        options_layout = QVBoxLayout(options_group)
        
        self.clean_config_cb = QCheckBox("清理VSCode配置")
        self.clean_config_cb.setChecked(True)
        options_layout.addWidget(self.clean_config_cb)
        
        self.clean_cache_cb = QCheckBox("清理VSCode缓存")
        self.clean_cache_cb.setChecked(True)
        options_layout.addWidget(self.clean_cache_cb)
        
        self.backup_cb = QCheckBox("备份VSCode数据")
        self.backup_cb.setChecked(True)
        options_layout.addWidget(self.backup_cb)
        
        layout.addWidget(options_group)
        
        # 按钮
        buttons_layout = QHBoxLayout()
        
        clean_btn = QPushButton("执行清理")
        clean_btn.clicked.connect(self.clean_vscode)
        buttons_layout.addWidget(clean_btn)
        
        verify_btn = QPushButton("验证清理")
        verify_btn.clicked.connect(self.verify_vscode_cleaning)
        buttons_layout.addWidget(verify_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        tabs.addTab(tab, "VSCode清理")
    
    def create_telemetry_tab(self, tabs):
        """创建Telemetry选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        disable_btn = QPushButton("禁用Telemetry")
        disable_btn.clicked.connect(self.disable_telemetry)
        layout.addWidget(disable_btn)
        
        generate_id_btn = QPushButton("生成新Machine ID")
        generate_id_btn.clicked.connect(self.generate_new_id)
        layout.addWidget(generate_id_btn)
        
        verify_btn = QPushButton("验证Telemetry状态")
        verify_btn.clicked.connect(self.verify_telemetry)
        layout.addWidget(verify_btn)
        
        layout.addStretch()
        
        tabs.addTab(tab, "Telemetry管理")
    
    def create_browser_tab(self, tabs):
        """创建浏览器选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 浏览器选择
        browser_group = QGroupBox("选择浏览器")
        browser_layout = QVBoxLayout(browser_group)
        
        self.browser_combo = QComboBox()
        self.browser_combo.addItems(["Chrome", "Firefox", "Edge", "Opera"])
        browser_layout.addWidget(self.browser_combo)
        
        layout.addWidget(browser_group)
        
        # 保护选项
        protection_group = QGroupBox("保护选项")
        protection_layout = QVBoxLayout(protection_group)
        
        self.canvas_cb = QCheckBox("Canvas指纹保护")
        self.canvas_cb.setChecked(True)
        protection_layout.addWidget(self.canvas_cb)
        
        self.webgl_cb = QCheckBox("WebGL指纹保护")
        self.webgl_cb.setChecked(True)
        protection_layout.addWidget(self.webgl_cb)
        
        self.headers_cb = QCheckBox("HTTP请求头修改")
        self.headers_cb.setChecked(True)
        protection_layout.addWidget(self.headers_cb)
        
        layout.addWidget(protection_group)
        
        # 按钮
        apply_btn = QPushButton("应用保护")
        apply_btn.clicked.connect(self.apply_browser_protection)
        layout.addWidget(apply_btn)
        
        verify_btn = QPushButton("验证保护状态")
        verify_btn.clicked.connect(self.verify_browser_protection)
        layout.addWidget(verify_btn)
        
        layout.addStretch()
        
        tabs.addTab(tab, "浏览器指纹")
    
    def create_hardware_tab(self, tabs):
        """创建硬件标识符选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # MAC地址部分
        mac_group = QGroupBox("MAC地址")
        mac_layout = QVBoxLayout(mac_group)
        
        # 添加适配器选择
        self.adapter_combo = QComboBox()
        self.refresh_adapters()  # 填充适配器列表
        mac_layout.addWidget(QLabel("网络适配器:"))
        mac_layout.addWidget(self.adapter_combo)
        
        mac_buttons = QHBoxLayout()
        
        change_mac_btn = QPushButton("更改MAC地址")
        change_mac_btn.clicked.connect(self.change_mac)
        mac_buttons.addWidget(change_mac_btn)
        
        verify_mac_btn = QPushButton("验证MAC地址")
        verify_mac_btn.clicked.connect(self.verify_mac)
        mac_buttons.addWidget(verify_mac_btn)
        
        mac_layout.addLayout(mac_buttons)
        layout.addWidget(mac_group)
        
        # UUID部分
        uuid_group = QGroupBox("系统UUID")
        uuid_layout = QVBoxLayout(uuid_group)
        
        uuid_buttons = QHBoxLayout()
        
        change_uuid_btn = QPushButton("更改系统UUID")
        change_uuid_btn.clicked.connect(self.change_uuid)
        uuid_buttons.addWidget(change_uuid_btn)
        
        verify_uuid_btn = QPushButton("验证系统UUID")
        verify_uuid_btn.clicked.connect(self.verify_uuid)
        uuid_buttons.addWidget(verify_uuid_btn)
        
        uuid_layout.addLayout(uuid_buttons)
        layout.addWidget(uuid_group)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新适配器列表")
        refresh_btn.clicked.connect(self.refresh_adapters)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        
        tabs.addTab(tab, "硬件标识符")
    
    def create_rotator_tab(self, tabs):
        """创建系统参数轮换选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 轮换设置
        settings_group = QGroupBox("轮换设置")
        settings_layout = QVBoxLayout(settings_group)
        
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("轮换间隔(秒):"))
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(60, 86400)  # 1分钟到1天
        self.interval_spin.setValue(3600)  # 默认1小时
        interval_layout.addWidget(self.interval_spin)
        
        settings_layout.addLayout(interval_layout)
        layout.addWidget(settings_group)
        
        # 按钮
        buttons_layout = QHBoxLayout()
        
        start_btn = QPushButton("启动轮换")
        start_btn.clicked.connect(self.start_rotation)
        buttons_layout.addWidget(start_btn)
        
        stop_btn = QPushButton("停止轮换")
        stop_btn.clicked.connect(self.stop_rotation)
        buttons_layout.addWidget(stop_btn)
        
        status_btn = QPushButton("轮换状态")
        status_btn.clicked.connect(self.check_rotation_status)
        buttons_layout.addWidget(status_btn)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        tabs.addTab(tab, "参数轮换")
    
    def create_eraser_tab(self, tabs):
        """创建安全擦除选项卡"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        scan_btn = QPushButton("扫描残留数据")
        scan_btn.clicked.connect(self.scan_residual)
        layout.addWidget(scan_btn)
        
        erase_btn = QPushButton("安全擦除数据")
        erase_btn.clicked.connect(self.secure_erase)
        layout.addWidget(erase_btn)
        
        warning_label = QLabel("警告: 安全擦除操作不可撤销，请谨慎使用!")
        warning_label.setStyleSheet("color: red;")
        layout.addWidget(warning_label)
        
        layout.addStretch()
        
        tabs.addTab(tab, "安全擦除")
    
    def check_admin_privileges(self):
        """检查管理员权限"""
        is_admin = False
        
        if platform.system() == "Windows":
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        else:
            is_admin = os.geteuid() == 0
        
        if not is_admin:
            self.logger.warning("未以管理员权限运行，某些功能可能不可用")
            QMessageBox.warning(self, "权限警告", "未以管理员权限运行，某些功能(如MAC地址修改)可能不可用。\n建议以管理员/root权限重新启动程序。")
    
    def refresh_adapters(self):
        """刷新网络适配器列表"""
        self.adapter_combo.clear()
        adapters = self.hw_identifier_manager.get_network_adapters()
        for adapter in adapters:
            self.adapter_combo.addItem(adapter)
    
    def refresh_status(self):
        """刷新状态信息"""
        try:
            # 更新轮换状态
            if hasattr(self, 'rotator') and self.rotator.rotation_active:
                self.status_label.setText("系统参数轮换: 活动")
            
            # 刷新其他状态...
        except Exception as e:
            self.logger.error(f"刷新状态异常: {str(e)}")
    
    def update_progress_bar(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status_label(self, text):
        """更新状态标签"""
        self.status_label.setText(text)
    
    def handle_operation_complete(self, success, message):
        """处理操作完成"""
        if success:
            self.logger.info(message)
            QMessageBox.information(self, "操作完成", message)
        else:
            self.logger.error(message)
            QMessageBox.critical(self, "操作失败", message)
    
    # VSCode相关操作
    def clean_vscode(self):
        """清理VSCode"""
        worker = VSCodeCleanerThread(
            self.vscode_cleaner,
            self.clean_config_cb.isChecked(),
            self.clean_cache_cb.isChecked(),
            self.backup_cb.isChecked()
        )
        worker.update_progress.connect(self.update_progress_bar)
        worker.update_status.connect(self.update_status_label)
        worker.operation_complete.connect(self.handle_operation_complete)
        worker.start()
    
    def verify_vscode_cleaning(self):
        """验证VSCode清理"""
        try:
            config_status = self.vscode_cleaner.verify_config()
            cache_status = self.vscode_cleaner.verify_cache()
            
            result = f"配置状态: {config_status}\n缓存状态: {cache_status}"
            self.logger.info(f"验证结果: {result}")
            QMessageBox.information(self, "验证结果", result)
        except Exception as e:
            self.logger.error(f"验证异常: {str(e)}")
            QMessageBox.critical(self, "验证失败", f"错误: {str(e)}")
    
    # Telemetry相关操作
    def disable_telemetry(self):
        """禁用Telemetry"""
        try:
            success, message = self.telemetry_manager.disable_telemetry()
            self.logger.info(f"禁用Telemetry: {message}")
            QMessageBox.information(self, "禁用Telemetry", message)
        except Exception as e:
            self.logger.error(f"禁用Telemetry异常: {str(e)}")
            QMessageBox.critical(self, "操作失败", f"错误: {str(e)}")
    
    def generate_new_id(self):
        """生成新Machine ID"""
        try:
            old_id = self.telemetry_manager.get_machine_id()
            success, new_id, message = self.telemetry_manager.generate_new_id()
            
            if success:
                log_msg = f"Machine ID更改: {old_id} -> {new_id}"
                self.logger.info(log_msg)
                QMessageBox.information(self, "生成新ID", f"{message}\n\n{log_msg}")
            else:
                self.logger.error(f"生成新ID失败: {message}")
                QMessageBox.critical(self, "操作失败", message)
        except Exception as e:
            self.logger.error(f"生成新ID异常: {str(e)}")
            QMessageBox.critical(self, "操作失败", f"错误: {str(e)}")
    
    def verify_telemetry(self):
        """验证Telemetry状态"""
        try:
            status = self.telemetry_manager.verify_telemetry_status()
            self.logger.info(f"Telemetry状态: {status}")
            QMessageBox.information(self, "Telemetry状态", status)
        except Exception as e:
            self.logger.error(f"验证Telemetry状态异常: {str(e)}")
            QMessageBox.critical(self, "验证失败", f"错误: {str(e)}")
    
    # 浏览器相关操作
    def apply_browser_protection(self):
        """应用浏览器保护"""
        try:
            browser = self.browser_combo.currentText()
            canvas = self.canvas_cb.isChecked()
            webgl = self.webgl_cb.isChecked()
            headers = self.headers_cb.isChecked()
            
            success, message = self.browser_fp_manager.apply_protection(
                browser, canvas, webgl, headers
            )
            
            self.logger.info(f"浏览器保护应用: {message}")
            QMessageBox.information(self, "浏览器保护", message)
        except Exception as e:
            self.logger.error(f"应用浏览器保护异常: {str(e)}")
            QMessageBox.critical(self, "操作失败", f"错误: {str(e)}")
    
    def verify_browser_protection(self):
        """验证浏览器保护状态"""
        try:
            browser = self.browser_combo.currentText()
            status = self.browser_fp_manager.verify_protection(browser)
            self.logger.info(f"浏览器保护状态: {status}")
            QMessageBox.information(self, "保护状态", status)
        except Exception as e:
            self.logger.error(f"验证浏览器保护状态异常: {str(e)}")
            QMessageBox.critical(self, "验证失败", f"错误: {str(e)}")
    
    # 硬件标识符相关操作
    def change_mac(self):
        """更改MAC地址"""
        adapter = self.adapter_combo.currentText()
        
        if not adapter:
            QMessageBox.warning(self, "警告", "请选择网络适配器")
            return
        
        worker = HardwareIDThread(
            self.hw_identifier_manager,
            change_mac=True,
            change_uuid=False,
            adapter_name=adapter
        )
        worker.update_progress.connect(self.update_progress_bar)
        worker.update_status.connect(self.update_status_label)
        worker.operation_complete.connect(self.handle_operation_complete)
        worker.start()
    
    def verify_mac(self):
        """验证MAC地址"""
        try:
            adapter = self.adapter_combo.currentText()
            
            if not adapter:
                QMessageBox.warning(self, "警告", "请选择网络适配器")
                return
            
            current_mac = self.hw_identifier_manager.get_current_mac(adapter)
            self.logger.info(f"当前MAC地址({adapter}): {current_mac}")
            QMessageBox.information(self, "MAC地址", f"适配器: {adapter}\nMAC地址: {current_mac}")
        except Exception as e:
            self.logger.error(f"验证MAC地址异常: {str(e)}")
            QMessageBox.critical(self, "验证失败", f"错误: {str(e)}")
    
    def change_uuid(self):
        """更改系统UUID"""
        worker = HardwareIDThread(
            self.hw_identifier_manager,
            change_mac=False,
            change_uuid=True
        )
        worker.update_progress.connect(self.update_progress_bar)
        worker.update_status.connect(self.update_status_label)
        worker.operation_complete.connect(self.handle_operation_complete)
        worker.start()
    
    def verify_uuid(self):
        """验证系统UUID"""
        try:
            current_uuid = self.hw_identifier_manager.get_current_uuid()
            self.logger.info(f"当前系统UUID: {current_uuid}")
            QMessageBox.information(self, "系统UUID", f"当前UUID: {current_uuid}")
        except Exception as e:
            self.logger.error(f"验证UUID异常: {str(e)}")
            QMessageBox.critical(self, "验证失败", f"错误: {str(e)}")
    
    # 系统参数轮换相关操作
    def start_rotation(self):
        """启动系统参数轮换"""
        try:
            interval = self.interval_spin.value()
            success, message = self.rotator.start_rotation(interval)
            self.logger.info(f"启动轮换: {message}")
            QMessageBox.information(self, "参数轮换", message)
        except Exception as e:
            self.logger.error(f"启动轮换异常: {str(e)}")
            QMessageBox.critical(self, "操作失败", f"错误: {str(e)}")
    
    def stop_rotation(self):
        """停止系统参数轮换"""
        try:
            success, message = self.rotator.stop_rotation()
            self.logger.info(f"停止轮换: {message}")
            QMessageBox.information(self, "参数轮换", message)
        except Exception as e:
            self.logger.error(f"停止轮换异常: {str(e)}")
            QMessageBox.critical(self, "操作失败", f"错误: {str(e)}")
    
    def check_rotation_status(self):
        """检查轮换状态"""
        try:
            status = "活动" if self.rotator.rotation_active else "未活动"
            interval = self.rotator.rotation_interval
            
            message = f"轮换状态: {status}\n轮换间隔: {interval}秒"
            self.logger.info(message)
            QMessageBox.information(self, "轮换状态", message)
        except Exception as e:
            self.logger.error(f"检查轮换状态异常: {str(e)}")
            QMessageBox.critical(self, "操作失败", f"错误: {str(e)}")
    
    # 安全擦除相关操作
    def scan_residual(self):
        """扫描残留数据"""
        try:
            success, message = self.secure_eraser.scan_residual_data()
            self.logger.info(f"扫描结果: {message}")
            QMessageBox.information(self, "扫描结果", message)
        except Exception as e:
            self.logger.error(f"扫描异常: {str(e)}")
            QMessageBox.critical(self, "扫描失败", f"错误: {str(e)}")
    
    def secure_erase(self):
        """安全擦除数据"""
        reply = QMessageBox.warning(
            self,
            "警告",
            "安全擦除操作将删除所有VSCode缓存和日志数据，这些操作不可撤销!\n确定要继续吗?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            worker = SecureEraseThread(self.secure_eraser)
            worker.update_progress.connect(self.update_progress_bar)
            worker.update_status.connect(self.update_status_label)
            worker.operation_complete.connect(self.handle_operation_complete)
            worker.start()

class LogHandler(logging.Handler):
    """日志处理器，将日志显示在UI中"""
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        self.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    
    def emit(self, record):
        msg = self.format(record)
        self.text_widget.append(msg)
        self.text_widget.ensureCursorVisible()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
