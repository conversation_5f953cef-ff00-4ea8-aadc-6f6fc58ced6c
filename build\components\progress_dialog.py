from PyQt5.QtWidgets import <PERSON><PERSON>ialog, QProgressBar, QLabel, QPushButton, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer

class ProgressDialog(QDialog):
    """进度对话框"""
    def __init__(self, parent=None, title="操作进行中", cancelable=True, timeout=60):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.resize(400, 150)
        
        # 设置超时时间（秒）
        self.timeout = timeout
        self.remaining_time = timeout
        self.timer = QTimer(self)
        self.timer.setInterval(1000)  # 1秒更新一次
        self.timer.timeout.connect(self._update_timer)
        
        # 创建UI元素
        self.status_label = QLabel("准备中...")
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        
        self.time_label = QLabel(f"剩余时间: {self.remaining_time}秒")
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setEnabled(cancelable)
        self.cancel_button.clicked.connect(self.reject)
        
        # 布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        
        main_layout = QVBoxLayout()
        main_layout.addWidget(self.status_label)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(self.time_label)
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
    
    def start(self):
        """开始计时并显示对话框"""
        self.timer.start()
        self.exec_()
    
    def _update_timer(self):
        """更新计时器"""
        self.remaining_time -= 1
        self.time_label.setText(f"剩余时间: {self.remaining_time}秒")
        
        if self.remaining_time <= 0:
            # 超时自动关闭
            self.timer.stop()
            self.reject()  # 拒绝对话框，相当于取消
    
    def set_progress(self, value):
        """设置进度值"""
        self.progress_bar.setValue(value)
    
    def set_status(self, text):
        """设置状态文本"""
        self.status_label.setText(text)
        
    def reset_timer(self):
        """重置计时器"""
        self.remaining_time = self.timeout
        self.time_label.setText(f"剩余时间: {self.remaining_time}秒")
