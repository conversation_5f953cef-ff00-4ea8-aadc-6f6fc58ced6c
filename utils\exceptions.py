"""
自定义异常类和错误处理工具
"""

class CodePrivacyError(Exception):
    """CodePrivacy基础异常类"""
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details

class PermissionError(CodePrivacyError):
    """权限不足异常"""
    def __init__(self, message="权限不足", details=None):
        super().__init__(message, "PERMISSION_DENIED", details)

class ConfigurationError(CodePrivacyError):
    """配置错误异常"""
    def __init__(self, message="配置错误", details=None):
        super().__init__(message, "CONFIG_ERROR", details)

class SystemOperationError(CodePrivacyError):
    """系统操作错误异常"""
    def __init__(self, message="系统操作失败", details=None):
        super().__init__(message, "SYSTEM_ERROR", details)

class BrowserError(CodePrivacyError):
    """浏览器操作错误异常"""
    def __init__(self, message="浏览器操作失败", details=None):
        super().__init__(message, "BROWSER_ERROR", details)

class NetworkError(CodePrivacyError):
    """网络操作错误异常"""
    def __init__(self, message="网络操作失败", details=None):
        super().__init__(message, "NETWORK_ERROR", details)

class SecurityError(CodePrivacyError):
    """安全操作错误异常"""
    def __init__(self, message="安全操作失败", details=None):
        super().__init__(message, "SECURITY_ERROR", details)

def handle_error(logger, operation, error, return_tuple=True):
    """
    统一错误处理函数
    
    Args:
        logger: 日志记录器
        operation: 操作名称
        error: 异常对象
        return_tuple: 是否返回元组格式 (success, message)
    
    Returns:
        如果return_tuple=True，返回 (False, error_message)
        否则重新抛出异常
    """
    if isinstance(error, CodePrivacyError):
        error_msg = f"{operation}失败: {error.message}"
        if error.details:
            error_msg += f" (详细信息: {error.details})"
        logger.error(error_msg)
        
        if return_tuple:
            return False, error.message
        else:
            raise error
    
    elif isinstance(error, PermissionError):
        error_msg = f"{operation}失败: 权限不足"
        logger.error(error_msg)
        
        if return_tuple:
            return False, "权限不足，请以管理员身份运行程序"
        else:
            raise PermissionError(error_msg)
    
    elif isinstance(error, FileNotFoundError):
        error_msg = f"{operation}失败: 文件或目录不存在"
        logger.error(error_msg)
        
        if return_tuple:
            return False, "文件或目录不存在"
        else:
            raise ConfigurationError(error_msg)
    
    else:
        error_msg = f"{operation}失败: {str(error)}"
        logger.error(error_msg, exc_info=True)
        
        if return_tuple:
            return False, f"操作失败: {str(error)}"
        else:
            raise SystemOperationError(error_msg, details=str(error))

def safe_execute(func, logger, operation_name, *args, **kwargs):
    """
    安全执行函数，自动处理异常
    
    Args:
        func: 要执行的函数
        logger: 日志记录器
        operation_name: 操作名称
        *args, **kwargs: 传递给函数的参数
    
    Returns:
        (success, result_or_error_message)
    """
    try:
        result = func(*args, **kwargs)
        logger.info(f"{operation_name}成功完成")
        return True, result
    except Exception as e:
        return handle_error(logger, operation_name, e, return_tuple=True)

def validate_admin_privileges():
    """验证管理员权限"""
    import platform
    import ctypes
    import os
    
    if platform.system() == "Windows":
        try:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        except:
            is_admin = False
    else:
        is_admin = os.geteuid() == 0
    
    if not is_admin:
        raise PermissionError(
            "需要管理员权限才能执行此操作",
            details="请以管理员/root身份重新启动程序"
        )
    
    return True

def validate_file_access(file_path, mode='r'):
    """验证文件访问权限"""
    import os
    
    if not os.path.exists(file_path):
        raise ConfigurationError(
            f"文件不存在: {file_path}",
            details="请检查文件路径是否正确"
        )
    
    if mode in ['w', 'a'] and not os.access(file_path, os.W_OK):
        raise PermissionError(
            f"无权限写入文件: {file_path}",
            details="请检查文件权限或以管理员身份运行"
        )
    
    if mode == 'r' and not os.access(file_path, os.R_OK):
        raise PermissionError(
            f"无权限读取文件: {file_path}",
            details="请检查文件权限"
        )
    
    return True
